<resources>
    <string name="have_account">Not yet registered? <u>Signup</u>.</string>
    <string name="country_list">Select Country</string>
    <string name="add_your_mobile_number">Add your Mobile number</string>
    <string name="we_ll_need_to_confirmation">We’ll need to confirmation it by sending a OTP</string>
    <string name="enter_mobile_number">Enter Mobile Number</string>
    <string name="by_continuing_you_confirm_that_you">By continuing, you confirm that you’re the owner or primary user of this mobile phone number. You agree to receive automated texts to confirm your phone number. Message and data rates may apply.</string>
    <string name="next">Next</string>
    <string name="create_guest">Sign up as a Guest on Keyless</string>
    <string name="user_name">User Name</string>
    <string name="password">Password</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="sign_up">Sign Up</string>
    <string name="forgot_password">Forgot Password</string>
    <string name="email">Email</string>
    <string name="enter_email">Enter Email</string>
    <string name="submit">Submit</string>
    <string name="sign_in_to_your_account">Login to your Account</string>
    <string name="mobile_number">Mobile Number</string>
    <string name="username_email">Username or Email</string>
    <string name="forgot_password_q">Forgot Password?</string>
    <string name="login">Login</string>
    <string name="check_your_phone">Check your phone</string>
    <string name="to_confirm_your_account_enter_the_4digit_code">To confirm your account, enter the 4 digit code sent to</string>
    <string name="didn_t_get_the_code">Didn’t get the verification code?</string>
    <string name="resend_code">Resend Code</string>
    <string name="verify_otp">Verify OTP</string>
    <string name="did_not_receive_the_email_check_your_nspam_filter_junk_mail">Did not receive the email? Check your\nspam filter, junk mail</string>
    <string name="or">or</string>
    <string name="use_another_email_address">Use another email address</string>
    <string name="verify_your_email">Verify your email</string>
    <string name="enter_the_email_which_you_want_to_associate_with_your_accountand_we_ll_send_an_email_with_verification_link">Enter the email which you want to associate with your account and we’ll send an email with verification OTP.</string>
    <string name="verify_email">Verify Email</string>
    <string name="alert">Alert</string>
    <string name="no">No</string>
    <string name="yes">Yes</string>
    <string name="keyless">KeyLess</string>
    <string name="ok">Ok</string>
    <string name="all_your_locks_from_one_app">All your locks from one app.</string>

    <string name="please_enter_email">Please enter Username or Email</string>
    <string name="please_enter_valid_password">Please enter your password</string>
    <string name="please_enter_username">Please enter username.</string>
    <string name="please_enter_password">Please enter password.</string>
    <string name="please_enter_confirm_password">Please enter confirm password.</string>
    <string name="password_not_match">Password not match!</string>
    <string name="logout">Logout</string>
    <string name="map">Map</string>
    <string name="list">List</string>
    <string name="buildings">Buildings</string>
    <string name="lock">Lock</string>
    <string name="home">Home</string>
    <string name="find_master_key_description">Turn on the Bluetooth on the Masterkey and press the Find Now button below to proceed.Make sure only 1 key is turned on so the right key is configured.</string>
    <string name="find_near_by_master_key">Find near by master key</string>
    <string name="btn_Next">Next</string>
    <string name="enable_bluetooth">Enable Bluetooth</string>
    <string name="enable_bluetooth_description">Please enable Bluetooth on your device to connect to the lock.</string>
    <string name="selected_key">Selected Key</string>
    <string name="select_lock">Select Lock</string>
    <string name="valid_till">Valid Till</string>
    <string name="configure_key">Configure Key</string>
    <string name="btnSave">Save</string>
    <string name="configure">Configure</string>
    <string name="routines" translatable="false">Routines</string>
    <string name="add_routine">Add Routine</string>
    <string name="routine_name">Routine Name</string>
    <string name="add_more">Add More</string>
    <string name="more">More</string>
    <string name="Signout">Sign Out</string>
    <string name="add_building">Add Building</string>
    <string name="myprofile">My Profile</string>
    <string name="firstname">First Name</string>
    <string name="last_name">Last Name</string>
    <string name="company_profile">Company Profile</string>
    <string name="company_name">Company Name</string>
    <string name="address">Address</string>
    <string name="country">Country</string>
    <string name="zip_code">Zip Code</string>
    <string name="city">City</string>
    <string name="trn">TRN</string>
    <string name="manage_staff">Manage Staff</string>
    <string name="call">Call</string>
    <string name="support">Support</string>
    <string name="addstaff">Add Staff</string>
    <string name="role">Role</string>
    <string name="passportname">Passport Number</string>
    <string name="current_password">Current Password</string>
    <string name="new_password">New Password</string>
    <string name="update_password">Update Password</string>
    <string name="about_us">About Us</string>
    <string name="about_the_app">About the app</string>
    <string name="about_body1">The Keyless app turns your mobile phone into a smart key, providing secure and easy access to rental properties, offices, Airbnbs and homes. No more key handovers. No more nights spent locked out. No keys. No fobs. No keycards. Simply lock and unlock the door using your phone. \nA single app that can be used by property managers/hosts and guests/residents.</string>
    <string name="about_body2">For property managers, the Keyless app takes the hassle out of hosting or managing multiple properties. Set up occupant profiles, time parameters for when guests can enter their property and internal access rights from the mobile app or web application.</string>
    <string name="about_body3">Keyless technology is a game-changer for everyone from property managers to holidaymakers, Airbnb hosts to frequent business travelers. This super app does more than just keep your property secure; it provides easy access to useful local services, including food delivery and emergency support. \nBenefits of using Keyless: \n• Receive your key before arrival \n• No key exchanges \n• Easy check-in and check-out \n• Manage all properties from one place \n• Register and maintain locks \n• A super app that can serve you in all ways needed</string>
    <string name="property_manager">Property managers</string>
    <string name="guest">Guest</string>
    <string name="terms_of_use">Terms of use</string>
    <string name="privacy">Privacy</string>
    <string name="explore_device">Find Nearby Device</string>
    <string name="by_signing_up_you_agree_to_the_terms_and_conditions">By signing up, you agree to the <u>terms and conditions</u></string>
    <string name="support_call_number">Support Call Number</string>
    <string name="support_whatsapp_number">Support Whatsapp Number</string>
    <string name="add_lock">Add Lock</string>
    <string name="brand">Brand</string>
    <string name="lock_model">Lock Model</string>
    <string name="internal_id">Internal Id</string>
    <string name="time_zone">Time Zone</string>
    <string name="select_time_zone">Select Time Zone</string>
    <string name="sizes">Sizes</string>
    <string name="color">Color</string>
    <string name="select_size">Select Size</string>
    <string name="select_color">Select Color</string>
    <string name="select_icon">Select Icon</string>
    <string name="select_location">Select Location</string>
    <string name="emirate">Emirate</string>
    <string name="building_name">Building Name</string>
    <string name="total_floor">Total Floor</string>
    <string name="maintenance_number">Maintenance Number</string>
    <string name="laundry_number">Laundry Number</string>
    <string name="enter_routine_name">Enter Routine Name</string>
    <string name="delete_routines">Delete Routines</string>
    <string name="add_user">Add User</string>
    <string name="enter_email_of_user_you_want_to_add">Enter email of user you want to add</string>
    <string name="this_user_does_not_exist_and_an_invite_email_will_be_sent_to_him_her">This user does not exist and an invite email will be sent to him/her.</string>
    <string name="already_shared">Already Shared</string>
    <string name="routine">Routine</string>
    <string name="enter_routine">Enter Routine</string>
    <string name="start_date">Start Date</string>
    <string name="enter_start_date">Enter Start Date</string>
    <string name="end_date">End Date</string>
    <string name="enter_end_date">Enter End Date</string>
    <string name="shared_access">Shared Access</string>
    <string name="mobile_key">Mobile Key</string>
    <string name="connecting_to_door_lock">Connecting to Door Lock</string>
    <string name="more_info">More Info</string>
    <string name="lock_history">Lock History</string>
    <string name="shared">Shared</string>
    <string name="no_internet_connection">No Internet Connection</string>
    <string name="lock_info">Lock Info</string>
    <string name="fetching_location">Fetching location...</string>
    <string name="location">Location</string>
    <string name="refresh">Refresh</string>
    <string name="pass_hint">∗∗∗∗∗∗∗∗∗</string>
    <string name="change_password">Change Password</string>
    <string name="no_of_floor">No of Floor:</string>
    <string name="locks">Locks:</string>
    <string name="maintenance_number_dot">Maintenance Number:</string>
    <string name="laundry_number_dot">Laundry Number:</string>
    <string name="property_location">Property Details</string>
    <string name="shared_with">Shared With</string>
    <string name="no_shared_access">No Shared Access</string>
    <string name="find_nearby_device">Find nearby device</string>
    <string name="contact_keyless_support">Contact Keyless Support</string>
    <string name="start_time">Start Time</string>
    <string name="end_time">End Time</string>
    <string name="enter_start_time">Enter Start Time</string>
    <string name="enter_end_time">Enter End Time</string>
    <string name="select_day">Select Day</string>
    <string name="mon">Mon</string>
    <string name="tue">Tue</string>
    <string name="wed">Wed</string>
    <string name="thu">Thu</string>
    <string name="fri">Fri</string>
    <string name="sat">Sat</string>
    <string name="sun">Sun</string>
    <string name="groceries">Groceries</string>
    <string name="_91">+91</string>
    <string name="search">Search</string>
    <string name="view_details">View Details</string>
    <string name="direction">Direction</string>
    <string name="create_lock">Create Lock</string>
    <string name="lock_name">Lock Name</string>
    <string name="floor_number">Floor Number</string>
    <string name="apartment_number_room_number">Apartment Number/Room Number</string>
    <string name="create_new_password">Create New Password</string>
    <string name="confirm_new_password">Confirm New Password</string>
    <string name="your_keys">Your Keys</string>
    <string name="no_locks_assigned">No Locks Assigned</string>
    <string name="quick_services">Quick Services</string>
    <string name="my_profile">My Profile</string>
    <string name="enable_screen_lock">Biometric access</string>
    <string name="search_by_building_name">Search by Building name</string>
    <string name="select_routine">Select Routine</string>
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="scan_the_qr_code_placed_on_the_lock_to_add_it_to_your_account">Scan the QR code placed on the lock to add it to your account.</string>
    <string name="your_lock_has_been_detected">Your lock has been detected</string>
    <string name="lock_type">Brand:</string>
    <string name="unlock_to_verify">Unlock to verify</string>
    <string name="press_button_to_unlock_your_lock">Press Button to Unlock your Lock</string>
    <string name="instashop">Instashop</string>
    <string name="noon_daily">noon daily</string>
    <string name="add_master_key">Add Master Key</string>
    <string name="access_granted">Access: Granted</string>
    <string name="living_room_lock">Living Room Lock</string>
    <string name="search_by_name">Search by name</string>
    <string name="save">Save</string>
    <string name="office_lock">Office Lock</string>
    <string name="are_you_sure_you_want_to_delete">Are you sure you want to delete this lock? The lock will be reset to factory settings.</string>
    <string name="taxi">Taxi</string>
    <string name="food_delivery">Food Delivery</string>
    <string name="house_keeping">House Keeping</string>
    <string name="emergency_services">Emergency Services</string>
    <string name="concierge">Concierge</string>
    <string name="local_experiences">Local Experiences</string>
    <string name="lock_deleted_successfully">Lock Deleted Successfully!</string>
    <string name="please_select_icon">Please select icon</string>
    <string name="please_enter_lock_name">Please enter lock name</string>
    <string name="please_select_building_name">Please select building name</string>
    <string name="please_enter_floor_number">Please enter floor number</string>
    <string name="please_enter_apartment_number">Please enter apartment number</string>
    <string name="must_need_camera_permission">Must need Camera permission</string>
    <string name="cannot_scan_this_code">Cannot scan this code!</string>
    <string name="the_lock_could_not_be_connected">The lock could not be connected to unlock. Either its connected to some other device, not in range or faulty.</string>
    <string name="select_timezone_to_continue">Select timezone to continue.</string>
    <string name="please_enter_brand">Please enter brand</string>
    <string name="please_enter_internal_id">Please enter Internal Id</string>
    <string name="please_enter_lock_model">Please enter Lock Model</string>
    <string name="please_enter_timezone">Please enter timezone</string>
    <string name="please_connect_internet">Please connect to the internet and open your app to refresh the Key access</string>
    <string name="no_access_history_found">No Access History Found</string>
    <string name="no_shared_history_found">No Shared History Found</string>
    <string name="revoked">Revoked</string>
    <string name="granted">Granted</string>
    <string name="building_name_dot">Building Name:</string>
    <string name="floor_number_dot">Floor Number:</string>
    <string name="apartment_room_number_dot">Apartment/Room Number:</string>
    <string name="battery_level_dot">Battery Level:</string>
    <string name="text_low">Low</string>
    <string name="text_average">Medium</string>
    <string name="text_good">High</string>
    <string name="text_full">Full</string>
    <string name="lock_not_accessible">Lock not accessible as privacy mode has been switched on</string>
    <string name="lock_is_in_dnd">Lock is in DND Mode, Do you want to override DND Mode?</string>
    <string name="text_off">off</string>
    <string name="access_denied">Access Denied! You do not have permission outside routine hours.</string>
    <string name="door_unlocked">Door Unlocked</string>
    <string name="tap_to_unlock_door">Tap to Unlock Door</string>
    <string name="tap_to_lock_door">Tap to Lock Door</string>
    <string name="lock_status_is">Lock status: %s call support</string>
    <string name="lock_status_is_tap_unlock">Lock Status: %s Tap to Unlock</string>
    <string name="property_services">Property Services</string>
    <string name="text_history">History</string>
    <string name="text_lock_info">Lock Info</string>
    <string name="text_lock_privacy">Lock Privacy</string>
    <string name="text_share_access">Share Access</string>
    <string name="text_support">Support</string>
    <string name="call_laundry">Call Laundry</string>
    <string name="call_maintenance">Call Maintenance</string>
    <string name="dont_have_permission_to_change_lock_privacy">You don\'t have permission to change lock privacy.</string>
    <string name="please_enter_start_date">PLease enter start date</string>
    <string name="please_enter_end_date">PLease enter end date</string>
    <string name="share_access_and_save"><![CDATA[Share Access & Save]]></string>
    <string name="want_to_cancel_invitation">Are you sure you want to cancel invitation?</string>
    <string name="want_to_revoke_lock">Are you sure you want to revoke this lock?</string>
    <string name="revoke">Revoke</string>
    <string name="login_with_otp">Login with OTP</string>
    <string name="welcome_to_keyless">Welcome to Keyless</string>
    <string name="share_access">Share access</string>
    <string name="one_application">One application to manage all your keys. Tap and unlock!</string>
    <string name="keyless_enables">Keyless enables you to share access to your property with your friends and family.</string>
    <string name="choose_from_a">Choose from a multitude of services to suit your desires. Enjoy your stay and leave the rest to us.</string>
    <string name="check_your_mail">Check your Email</string>
    <string name="socket_timeout_exception">Socket Timeout Exception</string>
    <string name="you_are_in_offline">You are in offline mode.</string>
    <string name="server_error">Server Error</string>
    <string name="something_went_wrong">Something went wrong</string>
    <string name="login_again_here_to_continue">This user have already login in another device. Please login again here to continue.</string>
    <string name="privacy_mode_on">When privacy mode is turned on, other users will not be able to unlock your property.</string>
    <string name="please_connect_internet_24_hr_notification">Please connect to the internet and open your app in the next 24 hrs to refresh the key and maintain access</string>
    <string name="offline_48_hr_notification">You have been offline for the last 48 hours. Please connect to the internet and open your app to refresh the Lock access</string>
    <string name="text_villa">Villa</string>
    <string name="text_hotel">Hotel</string>
    <string name="text_office">Office</string>
    <string name="text_apartment">Apartment</string>
    <string name="whatsapp_not_installed">WhatsApp not Installed</string>
    <string name="please_enter_routine_name">Please enter routine name</string>
    <string name="please_select_starting_time">Please select starting time</string>
    <string name="please_select_ending_time">Please select ending time</string>
    <string name="please_select_days">Please select days</string>
    <string name="end_time_should_greater_start_time">End time should be greater than Start time</string>
    <string name="do_you_want_delete_routine">Do you want to delete this routine?</string>
    <string name="do_you_delete_routine_slot">Do you want delete this routine slot?</string>
    <string name="update_routine">Update Routine</string>
    <string name="text_persons"> People</string>
    <string name="text_doors">Locks</string>
    <string name="please_allow_permissions_to_continue">Please allow permissions to continue</string>
    <string name="text_edit_property">Edit property</string>
    <string name="please_select_address">Please select address</string>
    <string name="please_select_emirate">Please select emirate</string>
    <string name="please_enter_building_name">Please enter building name</string>
    <string name="please_enter_total_floor">Please enter total floor</string>
    <string name="turn_on_gps_in_settings">Turn On GPS in settings</string>
    <string name="gps_is_not_enabled">GPS is not enabled. Do you want to go to settings menu?</string>
    <string name="text_settings">Settings</string>
    <string name="no_address_found">No Address found!</string>
    <string name="this_device_has_no_security">This device has no security. Please make this device password protected.</string>
    <string name="please_allow_bluetooth">To unlock the door, please switch on your device Bluetooth.</string>
    <string name="police_999">Police - 999</string>
    <string name="ambulance_998">Ambulance - 998</string>
    <string name="fire_department_997">Fire Department - 997</string>
    <string name="text_lock">Lock</string>
    <string name="text_locks">Locks</string>
    <string name="text_good_morning">Good Morning,</string>
    <string name="text_good_afternoon">Good Afternoon,</string>
    <string name="text_good_evening">Good Evening,</string>
    <string name="good_morning_guest">Good Morning, Guest</string>
    <string name="good_afternoon_guest">Good Afternoon, Guest</string>
    <string name="good_evening_guest">Good Evening, Guest</string>
    <string name="location_permission_needed">Location permission is needed to find nearby locks and connect to them. Please enable location permissions to proceed.</string>
    <string name="text_keyless">Keyless</string>
    <string name="your_session_expired">Your session has expired. Please login again to continue using the app</string>
    <string name="must_add_building">Before adding a lock, you need to add a building first.</string>
    <string name="text_person">Person</string>
    <string name="text_door">Door</string>
    <string name="please_add_atleast_one_routine">Please add atleast one routine</string>
    <string name="are_you_sure_you_want_to_logout">Are you sure you want to log out?</string>
    <string name="no_staff_member_found">No Staff member found</string>
    <string name="edit_access">Edit Access</string>
    <string name="update_access">Update Access</string>
    <string name="your_emergency_key_is_set">Your emergency key is configured. Now you can unlock the Door lock</string>
    <string name="please_enter_valid_to_date">Please select valid to date</string>
    <string name="whatsapp">Whatsapp</string>
    <string name="detecting_lock">Detecting Lock</string>
    <string name="no_properties">No Properties</string>
    <string name="change_language">Language</string>
    <string name="txt_english">English</string>
    <string name="text_arabic">Arabic</string>
    <string name="arabic">Arabic</string>
    <string name="english">English</string>
    <string name="routine_bottom">Routine</string>
    <string name="version">Version</string>
    <string name="txt_floor">Floor</string>
    <string name="change_icon">Change Icon</string>
    <string name="edit_lock">Edit Lock</string>
    <string name="turn_on_gps">Please turn on Location.</string>
    <string name="no_routines">No Routines</string>
    <string name="search_by_internal_id_or_lock_id">Search by internal id or lock id</string>
    <string name="select_the_rayonics_lock_to_be_used_for_adding_cards">Select the Rayonics lock to be used for adding cards.</string>
    <string name="next_card_id_to_be_generated">Next Card Id to be generated</string>
    <string name="tap_the_card">Tap the card to the selected lock to read the Card ID. Once read, the Card ID will be populated Below</string>
    <string name="card_id">Card ID</string>
    <string name="add_card">Add Card</string>
    <string name="add_ryonics_card">Add Rayonics Card</string>
    <string name="manage_cards">Manage Cards</string>
    <string name="select_rayonics_lock">Select Lock</string>
    <string name="selected_lock">Selected Lock</string>
    <string name="configure_card">Configure Card</string>
    <string name="no_card_found">No card found</string>
    <string name="card_to_be_configured">Card to be Configured</string>
    <string name="cards">Cards</string>
    <string name="search_by_card_name">Search by Card Name</string>
    <string name="id_has_been_created">id has been created for this lock.</string>
    <string name="delete_card">Delete Card</string>
    <string name="are_you_sure_you_want_delete_card">Are you sure you want to delete card?</string>
    <string name="please_tap_card_to_generate_card_id">Please Tap on Card to generate Card ID</string>
    <string name="not_assigned_lock">This lock is not assigned for installation to this Property Manager or is invalid.</string>
    <string name="error_in_installation">Error in Installation</string>
    <string name="please_put_your_comments_below_spo_the_admin_can_be_notified_the_error">Please put your comments below so the admin can be notified the error.</string>
    <string name="explain_the_issue_here">Explain the issue here</string>
    <string name="mark_as_failed">Mark as Failed</string>
    <string name="search_by_company_name">Search by Company name</string>
    <string name="install_locks">Install Lock</string>
    <string name="pending_installation">Pending Lock Installation (</string>
    <string name="pendin_lock">Lock)</string>
    <string name="pending_locks">Locks)</string>
    <string name="lock_pending_installation">lock pending installation</string>
    <string name="enter_email_id_of_cardholder">Enter Email id of cardholder</string>
    <string name="_971xxxxxxxxxx">971XXXXXXXXXX</string>
    <string name="password_changed_successfully">Password changed successfully.</string>
    <string name="not_allowed_on_rooted_device">The application is not allowed to run on a rooted device.</string>
    <string name="default_notification_channel_id" translatable="false">channel01</string>
    <string name="change_lock_battery">Change Lock battery</string>
    <string name="step_1_open_lock_cover_and_remove_old_batterystep_2_replace_with_new_battery_within_1_minutestep_3_close_the_lock_coveronce_step_3_is_completed_press_the_button_below_to_verify_lock_battery_status"><b>Step 1 -</b> Open Lock Cover and remove old battery\n\n<b>Step 2 -</b> Replace with new battery within 1 minute\n\n<b>Step 3 -</b> Close the Lock Cover\n\nOnce Step 3 is completed, press the button below to verify lock battery status.</string>
    <string name="installation">Installation</string>
    <string name="installation_details">Installation Details</string>
    <string name="location_details">Location Details</string>
    <string name="total_locks">Total Locks : </string>
    <string name="pending_locks1">Pending Locks : </string>


    <string name="home_health_care">Home Health Care</string>
    <string name="account_type">Account type</string>
    <string name="diagnostics">Diagnostics</string>
    <string name="trading_licence_number">Trade Licence Number</string>
    <string name="whtsapp_otp">Receive OTP via Whatsapp</string>
    <string name="sign_in">Login as user</string>
    <string name="select_user">Select User</string>
    <string name="search_by_email">Search by email</string>
    <string name="select_user_type">Select User Type</string>
    <string name="project_manager">Property Manager</string>
    <string name="select_project_manager_user">Select Property Manager User</string>
    <string name="select_guest_user">Select Guest User</string>
    <string name="no_user_found">No User Found</string>
    <string name="disabled_in_admin_mode">Disabled in Admin Mode</string>
    <string name="inactive">Inactive</string>
    <string name="claim_your_key">Claim your key</string>
    <string name="proceed">Proceed</string>
    <string name="tap_to_update_your_password_before">"Tap to Update your password before "</string>
    <string name="to_avoid_being_locked_out_of_your_account">" to avoid being locked out of your account."</string>
    <string name="end_date_time_should_be_greater_than_start_date">End date time should be greater than start date time</string>
    <string name="start_time_end_time_should_not_be_the_same">Start time &amp; end time should not be the same</string>
    <string name="update_available">Update Available</string>
    <string name="a_new_version_of_the_keyless_is_available_please_update_to_new_version_now">A new version of the keyless is available. Please update to new version now.</string>
    <string name="the_master_key_could_not_be_connected">The master key could not be connected</string>
    <string name="please_select_card_id_to_continue">Please select Card ID to continue</string>
    <string name="please_enter_email_to_continue">Please enter email to  continue</string>
    <string name="please_select_start_time_to_continue">Please select start time to  continue</string>
    <string name="please_select_end_time_to_continue">Please select end time to  continue</string>
    <string name="please_select_start_time_first">Please select Start time first!</string>
    <string name="please_enter_valid_email">Please enter valid email.</string>
    <string name="please_enter_last_name">Please enter last name</string>
    <string name="please_enter_first_name">Please enter first name</string>
    <string name="please_enter_mobile_number">Please enter mobile number.</string>
    <string name="please_enter_valid_mobile_number">Please enter valid mobile number.</string>
    <string name="please_enter_lock_model_to_continue">Please enter lock model to continue</string>
    <string name="bluetooth_not_enabled_by_user_or_it_will_not_support_by_device">Bluetooth not enabled by user or it will not support by device...</string>
    <string name="comment_should_not_be_empty">Comment should not be empty</string>
    <string name="no_locks_are_found">No locks are found</string>
    <string name="your_password_has_been_already_updated">Your password has been already updated.</string>
    <string name="please_select_lock_to_configure_key">Please select lock to configure key</string>
    <string name="return_to_admin">Return to Admin</string>
    <string name="xxride">XXRIDE</string>
    <string name="hala_taxi">Hala Taxi</string>
    <string name="brand1">Brand:</string>
    <string name="at_least_8_characters">At least 8 characters</string>
    <string name="containing_a_letter">Containing a letter</string>
    <string name="a_number">A Number</string>
    <string name="you_must_be_near_the_lock_to_perform_this_action">You must be near the lock to perform this action.</string>
    <string name="expired">Expired</string>
    <string name="you_cannot_delete_this_card">You cannot delete this card because it was created using the desktop app. You can delete it only from the Desktop app.</string>
    <string name="you_must_be_near_the_key_to_perform_this_action">You must be near the key to perform this action.</string>
    <string name="detecting_key">Detecting Key</string>


    <string-array name="DialingCountryCode">
        <item>32,BE</item>
        <item>501,BZ</item>
        <item>229,BJ</item>
        <item>975,BT</item>
        <item>591,BO</item>
        <item>387,BA</item>
        <item>267,BW</item>
        <item>55,BR</item>
        <item>673,BN</item>
        <item>359,BG</item>
        <item>226,BF</item>
        <item>95,MM</item>
        <item>257,BI</item>
        <item>855,KH</item>
        <item>237,CM</item>
        <item>1,CA</item>
        <item>238,CV</item>
        <item>236,CF</item>
        <item>235,TD</item>
        <item>56,CL</item>
        <item>86,CN</item>
        <item>61,CX</item>
        <item>61,CC</item>
        <item>57,CO</item>
        <item>269,KM</item>
        <item>242,CG</item>
        <item>243,CD</item>
        <item>682,CK</item>
        <item>506,CR</item>
        <item>385,HR</item>
        <item>53,CU</item>
        <item>357,CY</item>
        <item>93,AF</item>
        <item>355,AL</item>
        <item>213,DZ</item>
        <item>376,AD</item>
        <item>244,AO</item>
        <item>672,AQ</item>
        <item>54,AR</item>
        <item>374,AM</item>
        <item>297,AW</item>
        <item>61,AU</item>
        <item>43,AT</item>
        <item>994,AZ</item>
        <item>973,BH</item>
        <item>880,BD</item>
        <item>375,BY</item>
        <item>420,CZ</item>
        <item>45,DK</item>
        <item>253,DJ</item>
        <item>670,TL</item>
        <item>593,EC</item>
        <item>20,EG</item>
        <item>503,SV</item>
        <item>240,GQ</item>
        <item>358,FI</item>
        <item>33,FR</item>
        <item>291,ER</item>
        <item>372,EE</item>
        <item>251,ET</item>
        <item>500,FK</item>
        <item>298,FO</item>
        <item>679,FJ</item>
        <item>689,PF</item>
        <item>241,GA</item>
        <item>220,GM</item>
        <item>995,GE</item>
        <item>49,DE</item>
        <item>233,GH</item>
        <item>350,GI</item>
        <item>30,GR</item>
        <item>299,GL</item>
        <item>502,GT</item>
        <item>224,GN</item>
        <item>245,GW</item>
        <item>592,GY</item>
        <item>509,HT</item>
        <item>504,HN</item>
        <item>852,HK</item>
        <item>36,HU</item>
        <item>91,IN</item>
        <item>62,ID</item>
        <item>98,IR</item>
        <item>964,IQ</item>
        <item>353,IE</item>
        <item>44,IM</item>
        <item>972,IL</item>
        <item>39,IT</item>
        <item>225,CI</item>
        <item>81,JP</item>
        <item>962,JO</item>
        <item>7,KZ</item>
        <item>254,KE</item>
        <item>686,KI</item>
        <item>965,KW</item>
        <item>996,KG</item>
        <item>856,LA</item>
        <item>371,LV</item>
        <item>961,LB</item>
        <item>266,LS</item>
        <item>231,LR</item>
        <item>218,LY</item>
        <item>423,LI</item>
        <item>370,LT</item>
        <item>352,LU</item>
        <item>853,MO</item>
        <item>389,MK</item>
        <item>261,MG</item>
        <item>265,MW</item>
        <item>60,MY</item>
        <item>960,MV</item>
        <item>223,ML</item>
        <item>356,MT</item>
        <item>692,MH</item>
        <item>222,MR</item>
        <item>230,MU</item>
        <item>262,YT</item>
        <item>52,MX</item>
        <item>691,FM</item>
        <item>373,MD</item>
        <item>377,MC</item>
        <item>976,MN</item>
        <item>382,ME</item>
        <item>212,MA</item>
        <item>258,MZ</item>
        <item>264,NA</item>
        <item>674,NR</item>
        <item>977,NP</item>
        <item>31,NL</item>
        <item>599,AN</item>
        <item>687,NC</item>
        <item>64,NZ</item>
        <item>505,NI</item>
        <item>227,NE</item>
        <item>234,NG</item>
        <item>683,NU</item>
        <item>850,KP</item>
        <item>47,NO</item>
        <item>968,OM</item>
        <item>92,PK</item>
        <item>680,PW</item>
        <item>507,PA</item>
        <item>675,PG</item>
        <item>595,PY</item>
        <item>51,PE</item>
        <item>63,PH</item>
        <item>870,PN</item>
        <item>48,PL</item>
        <item>351,PT</item>
        <item>1,PR</item>
        <item>974,QA</item>
        <item>40,RO</item>
        <item>7,RU</item>
        <item>250,RW</item>
        <item>590,BL</item>
        <item>685,WS</item>
        <item>378,SM</item>
        <item>239,ST</item>
        <item>966,SA</item>
        <item>221,SN</item>
        <item>381,RS</item>
        <item>248,SC</item>
        <item>232,SL</item>
        <item>65,SG</item>
        <item>421,SK</item>
        <item>386,SI</item>
        <item>677,SB</item>
        <item>252,SO</item>
        <item>27,ZA</item>
        <item>82,KR</item>
        <item>34,ES</item>
        <item>94,LK</item>
        <item>290,SH</item>
        <item>508,PM</item>
        <item>249,SD</item>
        <item>597,SR</item>
        <item>268,SZ</item>
        <item>46,SE</item>
        <item>41,CH</item>
        <item>963,SY</item>
        <item>886,TW</item>
        <item>992,TJ</item>
        <item>255,TZ</item>
        <item>66,TH</item>
        <item>228,TG</item>
        <item>690,TK</item>
        <item>676,TO</item>
        <item>216,TN</item>
        <item>90,TR</item>
        <item>993,TM</item>
        <item>688,TV</item>
        <item>971,AE</item>
        <item>256,UG</item>
        <item>44,GB</item>
        <item>380,UA</item>
        <item>598,UY</item>
        <item>1,US</item>
        <item>998,UZ</item>
        <item>678,VU</item>
        <item>39,VA</item>
        <item>58,VE</item>
        <item>84,VN</item>
        <item>681,WF</item>
        <item>967,YE</item>
        <item>260,ZM</item>
        <item>263,ZW</item>
    </string-array>
    <!-- Strings used for fragments for navigation -->

    <string name="dfu_channel_name" translatable="false">Progress</string>
    <string name="dfu_channel_description" translatable="false">DFU operation progress.</string>


    <string name="dfu_bootloader_idle" translatable="false">Enabling bootloader</string>
    <string name="dfu_bootloader_working" translatable="false">Enabling bootloader…</string>
    <string name="dfu_bootloader_success" translatable="false">Bootloader enabled</string>

    <string name="dfu_process_idle" translatable="false">DFU initialization</string>
    <string name="dfu_process_working" translatable="false">Initializing DFU…</string>
    <string name="dfu_process_success" translatable="false">DFU initialized</string>

    <string name="dfu_firmware_idle" translatable="false">Firmware upload</string>
    <string name="dfu_firmware_working" translatable="false">Uploading firmware…</string>
    <string name="dfu_firmware_success" translatable="false">Firmware uploaded</string>

    <string name="dfu_display_status_progress_update_parts" translatable="false">Uploading part %d/%d… %d\%%</string>
    <string name="dfu_display_status_progress_update" translatable="false">Uploading… %d\%%</string>
    <string name="dfu_display_status_progress_speed" translatable="false">%.1f kB/s</string>
    <string name="dfu_progress_stage_completed" translatable="false">Completed\n</string>
    <string name="dfu_progress_stage_error" translatable="false">Error: %s</string>
    <string name="dfu_unknown" translatable="false">Unknown error</string>
    <string name="dfu_error_link_loss" translatable="false">Device has disconnected</string>
    <string name="dfu_error_file_error" translatable="false">Invalid or too large file</string>
    <string name="dfu_error_file_unsupported" translatable="false">Unsupported file</string>
    <string name="dfu_error_file_type_invalid" translatable="false">The file type is not supported</string>
    <string name="dfu_error_not_supported" translatable="false">The Device does not support nRF5 DFU</string>
    <string name="dfu_error_bluetooth_disabled" translatable="false">Bluetooth is disabled</string>
    <string name="dfu_error_not_bonded" translatable="false">The device is not bonded</string>
    <string name="dfu_error_init_packet_required" translatable="false">The init packet is required</string>
    <string name="dfu_error_invalid_object" translatable="false">Selected firmware is not compatible with the device\n</string>
    <string name="dfu_error_insufficient_resources" translatable="false">Insufficient Resources</string>
    <string name="this_lock_is_already_on_latest_firmware_version">This lock is already on latest firmware version</string>
    <string name="there_is_an_error_in_the_firmware_file_please_contact_keyless_support">There is an error in the firmware file. Please contact Keyless Support</string>
    <string name="firmware_updated_successfully">Firmware updated successfully</string>
    <string name="morning_9am_1pm">Morning (9AM - 1PM)</string>
    <string name="evening_2pm_6pm">Evening (2PM - 6PM)</string>
    <string name="company_name_api">Company Name:</string>
    <string name="address_api">Address:</string>
    <string name="lock_name_dot">Lock Name:</string>
    <string name="brand_keyless">Brand: Keyless</string>
    <string name="download_completed">Download Completed</string>
    <string name="start_updating">Start Updating</string>
    <string name="uploading">Uploading</string>
    <string name="downloading_file">Downloading File</string>
    <string name="downloading_firmware">Downloading Firmware</string>
    <string name="connecting">Connecting</string>
    <string name="check_in">Check In</string>
    <string name="please_complete_your_check_in_process_using_either_your_passport_or_emirates_id_card">Please complete your check in process using either your passport or Emirates ID card.</string>
    <string name="configure_cards">Configured Cards</string>
    <string name="no_configured_card_found">No configured card found</string>
    <string name="configure_new_card">Configure New Card</string>
    <string name="complete_check_in">Complete Check-in</string>
    <string name="your_check_in_has_been_approved">Your Check-in has been approved.</string>
    <string name="your_check_in_will_be_submitted_for_approval">Your Check-in will be submitted for approval.</string>
    <string name="retry">Retry</string>
    <string name="your_check_in_has_failed">Your Check-in has failed.</string>
    <string name="scanning_cancelled">Scanning cancelled</string>
    <string name="passport">Passport</string>
    <string name="emirate_id">UAE Emirates ID</string>
    <string name="the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_the_front_side_of_the_passport">The scanning of the document has failed. Please make sure you are scanning the front side of the passport.</string>
    <string name="the_document_you_are_trying_to_upload_is_invalid">The document you are trying to upload is invalid</string>
    <string name="the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_both_front_and_back_side_of_the_document">The scanning of the document has failed. Please make sure you are scanning both front and back side of the document.</string>
    <string name="the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again">The uploaded document is either not valid or not properly scanned. Please try again.</string>
    <string name="valid">VALID</string>
    <string name="invalid">INVALID</string>
    <string name="check_in_">Check-In</string>
    <string name="check_in_required">Check-In Required</string>
    <string name="generate_passcode">Generate Passcode</string>
    <string name="generate_offline_passcode_helper_text">Please stay near the lock to revoke the passcode.\nNote that passcodes can only be generated to registered users.</string>
    <string name="generate_passcode_helper_text">Passcodes can only be generated to registered users.</string>
    <string name="enabling_this_feature_will_initiate_a_check_in_process_for_the_guest_to_upload_their_passport_documents_and_complete_a_facial_verification">Enabling this feature will initiate a check-in process for the guest to upload their passport documents and complete a facial verification.</string>
    <string name="start_check_in">Start Check-in (</string>
    <string name="completed">Completed)</string>
    <string name="please_wait">Please wait</string>
    <string name="upload_documents">Upload Documents</string>
    <string name="select_document_type">Select Document Type</string>
    <string name="front_side">Front Side</string>
    <string name="capture_document">Capture Document</string>
    <string name="back_side">Back Side</string>
    <string name="document_status">Document Status</string>
    <string name="search_by_name_and_booking_number">Search by name and booking number</string>
    <string name="summary">Summary</string>
    <string name="document_nverified">Document\nVerified</string>
    <string name="face_nmatch">Face\nMatch</string>
    <string name="liveness_ncomplete">Liveness\nComplete</string>
    <string name="name">Name:</string>
    <string name="document_type">Document Type:</string>
    <string name="document_expiry_date">Document Expiry Date:</string>
    <string name="document_status_">Document Status:</string>
    <string name="document_vs_camera_image">Document vs Camera Image</string>
    <string name="add_guest">Add Guest?</string>
    <string name="add_guest_btn">Add Guest</string>
    <string name="done">Done</string>
    <string name="want_another_check_in">Do you want to add check-in for another guest?\nNote: You can also do the check-in later from lock screen.</string>
    <string name="unit_number">Unit Number</string>
    <string name="clear">Clear</string>
    <string name="no_guest_found">No Guest Found</string>
    <string name="notifications">Notifications</string>
    <string name="please_enter_your_retrieval_code_to_claim_your_key">Please enter your retrieval code to claim your key</string>
    <string name="enter_retrieval_code_number">Enter retrieval code number</string>


</resources>