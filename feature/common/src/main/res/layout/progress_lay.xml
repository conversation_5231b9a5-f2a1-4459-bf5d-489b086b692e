<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="#33000000"
    android:clickable="true"
    android:focusable="true"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:gravity="center"
        android:background="@drawable/white_all_corners_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:theme="@style/progressBarBlue"
            android:progressTint="@color/colorAccent"
            android:progressBackgroundTint="@color/colorAccent"
            android:layout_height="wrap_content"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>