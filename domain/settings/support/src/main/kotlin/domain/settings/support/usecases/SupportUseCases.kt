package domain.settings.support.usecases

import core.common.status.StatusRepository
import core.locks.logs.repostiories.LockLogsRepository
import core.locks.manager.LocksManager
import core.monitoring.common.repository.Logger
import core.permissions.manager.BluetoothManager
import core.permissions.manager.PermissionsManager
import domain.settings.support.repositories.ScreenStateRepository
import domain.settings.support.repositories.SideEffectsRepository

internal class SupportUseCases(
    lockLogs: LockLogsRepository,
    logger: Logger,
    status: StatusRepository,
    screenRepository: ScreenStateRepository,
    manager: LocksManager,
    bluetooth: BluetoothManager,
    permissions: PermissionsManager,
    sideEffects: SideEffectsRepository
) {
    val sendLockLogs = SendLockLogsUseCase(lockLogs, sideEffects, logger, status)

    val screen = ScreenUseCase(
        state = screenRepository,
        logger = logger
    )

    val nearby = NearByUseCase(
        state = screenRepository,
        logger = logger,
        manager = manager,
        bluetooth = bluetooth,
        permissions = permissions
    )
}