<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.common.country.CountryListFragment">

    <TextView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/country_list"
        android:textColor="@color/white"
        android:fontFamily="@font/poppins_regular_400"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/onBackCountry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:padding="10dp"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginTop="20dp"
        android:background="@drawable/white_top_corners"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView">

        <EditText
            android:id="@+id/searchCountryET"
            style="@style/mirrorText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:layout_marginTop="30dp"
            android:singleLine="true"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/bg_edit_grey"
            android:drawableStart="@drawable/ic_baseline_search_24"
            android:drawablePadding="10dp"
            android:hint="@string/search"
            android:imeOptions="actionDone"
            android:padding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/stopSearch"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:padding="10dp"
            android:src="@drawable/iv_cross_black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/searchCountryET"
            app:layout_constraintEnd_toEndOf="@+id/searchCountryET"
            app:layout_constraintTop_toTopOf="@+id/searchCountryET" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/countryRV"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/searchCountryET" />
    </androidx.constraintlayout.widget.ConstraintLayout>





</androidx.constraintlayout.widget.ConstraintLayout>