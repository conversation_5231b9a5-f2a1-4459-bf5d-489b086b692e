@file:Suppress("PackageDirectoryMismatch", "unused")

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.Project
import org.gradle.api.artifacts.MinimalExternalModuleDependency
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.api.artifacts.VersionConstraint
import org.gradle.api.plugins.ExtensionAware
import org.gradle.api.provider.Provider
import org.gradle.kotlin.dsl.getByType
import org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions
import java.io.File
import java.util.Locale
import java.util.Properties

internal val Project.catalog
    get() = this.extensions.getByType<VersionCatalogsExtension>().named("keyless")

fun Project.getLocalProperty(
    key: String,
    file: String = "local.properties",
    recursive: Boolean = true
): Any? {
    val properties = Properties()
    val localProperties = File("$projectDir/$file")

    return when {
        localProperties.isFile -> {
            properties.load(localProperties.reader())
            properties.getProperty(key)
        }

        parent != null && recursive -> {
            parent?.getLocalProperty(key, file)
        }

        else -> null
    }
}

fun Project.getLocalPropertiesFromFile(name: String = "local"): Properties {
    val publishProperties = Properties().apply {
        val propertiesFile = File("$projectDir/$name.properties")
        if (propertiesFile.exists()) {
            load(propertiesFile.reader())
        }
    }

    return publishProperties
}

fun Project.setLocalProperty(
    values: Map<String, String>,
    file: String = ""
) {
    val properties = Properties()
    val fileName = if (file.isBlank()) "local.properties" else "$file.properties"
    val propertiesFile = File("$projectDir/$fileName")

    if (!propertiesFile.exists()) {
        propertiesFile.createNewFile()
    }

    properties.load(propertiesFile.reader())
    values.forEach {
        properties[it.key] = it.value
    }

    properties.store(propertiesFile.outputStream(), null)
}

fun Project.getAllChildren(): List<Project> {
    val list = arrayListOf<Project>()

    list.addAll(childProjects.map { it.value })
    list.addAll(childProjects.map { it.value.getAllChildren() }.flatten())

    return list
}

fun Project.namespace(): String {
    return "${rootProject.group}.$group.$name"
        .replace("/", ".")
        .replace("-", ".")
        .replace("_", ".")
        .removePrefix(".")
        .lowercase(Locale.getDefault())
}

val Project.nameOneWord
    get() = name.split("-", "_", " ", ".").joinToString("") {
        it.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
    }