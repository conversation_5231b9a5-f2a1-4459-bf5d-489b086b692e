<?xml version="1.0" encoding="utf-8"?><!--<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:id="@+id/container"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:orientation="vertical">-->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.home.installer.InstallerListFragment">


    <EditText
        android:id="@+id/svInstaller"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_btn_round"
        android:backgroundTint="@color/bg_edit_grey"
        android:drawableStart="@drawable/ic_baseline_search_24"
        android:drawablePadding="10dp"
        android:ellipsize="end"
        android:focusableInTouchMode="true"
        android:fontFamily="@font/poppins_regular_400"
        android:hint="@string/search_by_company_name"
        android:imeOptions="actionSearch"
        android:includeFontPadding="false"
        android:padding="8dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forcePassUpdateView" />



    <TextView
        android:id="@+id/forcePassUpdateView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/force_update_bg"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:visibility="gone"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <ImageView
        android:id="@+id/stopInstaller"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:src="@drawable/iv_cross_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/svInstaller"
        app:layout_constraintEnd_toEndOf="@+id/svInstaller"
        app:layout_constraintTop_toTopOf="@+id/svInstaller" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMaintenance"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/svInstaller"
        app:layout_constraintVertical_bias="0.0" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvInstaller"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/svInstaller"
        app:layout_constraintVertical_bias="0.0" />


    <ProgressBar
        android:id="@+id/progress_pagination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:theme="@style/progressBarBlue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/layNoData"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/iv_no_locks" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no_locks_assigned"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>

    <!--</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>-->