package feature.regula.refactored.checkin

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import domain.common.ErrorHandler
import domain.regula.models.Event
import feature.regula.refactored.checkin.models.toUIState
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class CheckInResultsAndroidViewModel(
    private val viewModel: domain.regula.ViewModel,
    private val handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    coroutineContext: CoroutineContext? = null,
    private val onClear: () -> Unit
) : ViewModel() {

    private val scope = if (coroutineContext != null) viewModelScope + coroutineContext else viewModelScope

    val screenStream = viewModel.screenStream.map { it.toUIState() }
    val sideEffects = viewModel.sideEffectsStream

    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}