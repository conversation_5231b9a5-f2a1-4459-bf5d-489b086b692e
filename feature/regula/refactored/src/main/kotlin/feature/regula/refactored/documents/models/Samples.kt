package feature.regula.refactored.documents.models

import androidx.compose.runtime.Composable
import domain.regula.models.ScreenState

internal object Samples {

    fun initState(): UIScreenState {
        return UILoadingState(status = emptyList())
    }

    @Composable
    fun sampleState(): UIScreenState {
        return UIReadyState(
            status = emptyList(),
            selectedDocumentType = null,
            documentTypes = listOf(ScreenState.DocumentTypeState.UAE_ID, ScreenState.DocumentTypeState.PASSPORT),
            frontSide = null,
            backSide = null
        )
    }
}