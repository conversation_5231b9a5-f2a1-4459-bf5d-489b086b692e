<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.authentication.signup.VerifyOtpFragment">


    <TextView
        android:id="@+id/textViewHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/check_your_phone"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtSubHeading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/to_confirm_your_account_enter_the_4digit_code"
        android:textColor="@color/black"
        android:textSize="16dp"
        style="@style/mirrorText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />


<!--    <LinearLayout-->
<!--        android:id="@+id/linearPin"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layoutDirection="rtl"-->
<!--        style="@style/mirrorText"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/txtSubHeading"-->
<!--        android:orientation="vertical">-->

<!--        <com.chaos.view.PinView-->
<!--            app:cursorColor="@color/black"-->
<!--            android:id="@+id/txtPinView"-->
<!--            android:textAlignment="viewStart"-->
<!--            android:textDirection="firstStrongLtr"-->
<!--            style="@style/PinWidget.PinView"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="24dp"-->
<!--            android:cursorVisible="true"-->
<!--            android:inputType="number"-->
<!--            android:itemBackground="@drawable/bg_edit_corners"-->
<!--            android:textSize="18dp"-->
<!--            android:textStyle="normal"-->
<!--            app:cursorWidth="2dp"-->
<!--            app:hideLineWhenFilled="true"-->
<!--            app:itemCount="4"-->
<!--            app:itemHeight="48dp"-->
<!--            app:itemRadius="10dp"-->
<!--            app:itemSpacing="24dp"-->
<!--            app:itemWidth="48dp"-->
<!--            app:lineColor="@color/white"-->
<!--            app:lineWidth="0dp"-->
<!--            app:viewType="rectangle" />-->


    <com.chaos.view.PinView
            android:id="@+id/txtPinView"
            style="@style/PinWidget.PinView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:cursorVisible="false"
            android:inputType="number"
            android:itemBackground="@drawable/bg_edit_corners"
            android:textSize="18dp"
            android:textStyle="normal"
            app:cursorWidth="2dp"
            app:hideLineWhenFilled="false"
            app:itemCount="4"
            app:itemHeight="48dp"
            app:itemRadius="10dp"
            app:itemSpacing="24dp"
            app:itemWidth="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtSubHeading"
            app:lineColor="@color/white"
            app:lineWidth="0dp"
            app:viewType="rectangle" />

<!--        <com.mukeshsolanki.OtpView-->
<!--            android:id="@+id/txtPinView"-->
<!--            app:OtpItemWidth="48dp"-->
<!--            app:OtpItemHeight="48dp"-->
<!--            app:OtpItemRadius="10dp"-->
<!--            app:OtpItemSpacing="20dp"-->
<!--            android:itemBackground="@drawable/bg_edit_corners"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="24dp"-->
<!--            android:inputType="number"-->
<!--            android:textColor="@android:color/black"-->
<!--            app:OtpItemCount="4"-->
<!--            android:cursorVisible="false"-->
<!--            app:OtpViewType="rectangle"-->
<!--            android:focusableInTouchMode="true"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/txtSubHeading"-->
<!--            />-->

<!--    </LinearLayout>-->



    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtPinView">


        <TextView
            android:id="@+id/openSheet"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:fontFamily="@font/poppins_medium_500"
            android:text="@string/didn_t_get_the_code"
            android:textColor="@color/black"
            android:textSize="15dp"
            app:layout_constraintEnd_toEndOf="parent" />
        <TextView
                android:id="@+id/openSheetPressHere"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:visibility="gone"
                tools:visibility="gone"
                android:fontFamily="@font/poppins_bold_700"
                android:text="@string/resend_code"
                android:textSize="15dp"
                android:gravity="center"
                android:textColor="@color/colorAccent"
                app:layout_constraintEnd_toEndOf="parent" />
        <TextView
            android:id="@+id/resendCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:visibility="gone"
            android:fontFamily="@font/poppins_bold_700"
            android:text="@string/resend_code"
            android:textSize="15dp"
            android:gravity="center"
            android:textColor="@color/colorAccent"
            app:layout_constraintEnd_toEndOf="parent" />


    </LinearLayout>


    <TextView
        android:id="@+id/textView38"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        android:text="@string/or"
        android:visibility="gone"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout3" />

    <TextView
        android:id="@+id/whtsappCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/whtsapp_otp"
        android:textColor="@color/black"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView38" />

    <TextView
        android:id="@+id/mTextField"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_medium_500"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/whtsappCode" />


    <TextView
        android:id="@+id/verifyOtpBtn"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/verify_otp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@+id/textView7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <TextView
        android:id="@+id/textView7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:text="@string/did_not_receive_the_email_check_your_nspam_filter_junk_mail"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/textView8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/textView8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        android:text="@string/or"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/textView6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <TextView
        android:id="@+id/textView6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/use_another_email_address"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>