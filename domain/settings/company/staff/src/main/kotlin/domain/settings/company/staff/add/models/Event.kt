package domain.settings.company.staff.add.models

import data.company.models.StaffMemberRole

sealed interface Event

sealed interface ScreenEvent : Event {
    class Init(val memberId: String?, val isVerified: Boolean) : ScreenEvent

    object ClickToSelectRole : ScreenEvent

    class SelectRole(val role: StaffMemberRole) : ScreenEvent
}

sealed interface StaffEvent : Event {
    class AddStaff(
        val countryCode: String,
        val mobileNo: String,
        val email: String,
        val userName: String,
        val firstName: String,
        val lastName: String,
        val roleId: String,
        val passport: String
    ) : StaffEvent

    class EditStaff(
        val staffId: String,
        val countryCode: String,
        val mobileNo: String,
        val email: String,
        val userName: String,
        val firstName: String,
        val lastName: String,
        val roleId: String,
        val passport: String
    ) : StaffEvent
}