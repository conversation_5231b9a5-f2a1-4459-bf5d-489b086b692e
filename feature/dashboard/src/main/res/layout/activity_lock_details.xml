<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.dashboard.unlock.LockDetailsActivity">

<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content">-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_marginTop="20dp"
            android:id="@+id/toolbar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <ImageView
                style="@style/ImageMirror"
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/iv_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/viewBack"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewBack"
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/mobile_key"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="20dp"
                android:background="@drawable/ic_map_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
<!--    </androidx.appcompat.widget.Toolbar>-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintVertical_bias="1.0">

        <androidx.core.widget.NestedScrollView
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/txtCheckIn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginStart="20dp"
                    android:background="@drawable/bg_blue_10_corner"
                    android:fontFamily="@font/poppins_regular_400"
                    android:includeFontPadding="false"
                    android:paddingStart="20dp"
                    android:paddingTop="15dp"
                    android:visibility="gone"
                    android:drawableTint="@color/white"
                    android:drawableEnd="@drawable/ic_arrow_forward"
                    android:paddingEnd="20dp"
                    android:paddingBottom="15dp"
                    android:textColor="@color/white"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearText" />



                <TextView
                    android:id="@+id/txtLockName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/poppins_medium_500"
                    android:includeFontPadding="false"
                    android:textColor="@color/black"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/tv_floor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:includeFontPadding="false"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="@+id/txtLockName"
                    app:layout_constraintTop_toBottomOf="@+id/txtLockName" />


                <ImageView
                    android:src="@drawable/ic_map_icon"
                    android:id="@+id/ivBatteryCicular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    style="@style/ImageMirror"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_floor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_floor" />

                <com.skyfishjy.library.RippleBackground
                    android:id="@+id/ripplView"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:elevation="11dp"
                    app:layout_constraintBottom_toBottomOf="@+id/cardView"
                    app:layout_constraintEnd_toEndOf="@+id/cardView"
                    app:layout_constraintStart_toStartOf="@+id/cardView"
                    app:layout_constraintTop_toTopOf="@+id/cardView"
                    app:rb_color="#1ABF7D1A"
                    app:rb_duration="2000"
                    app:rb_radius="10dp"
                    app:rb_rippleAmount="4"
                    app:rb_scale="6">

                    <ImageView
                        android:id="@+id/ivLock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_centerInParent="true"
                        android:background="@drawable/ic_close_lock"
                        android:padding="10dp" />

                </com.skyfishjy.library.RippleBackground>


                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView"
                    android:layout_width="198dp"
                    android:layout_height="198dp"
                    app:cardCornerRadius="100dp"
                    app:cardElevation="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_floor">


                    <ProgressBar
                        android:id="@+id/clRipple"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:layout_gravity="center"
                        android:background="@drawable/bg_grey_rounded"
                        android:indeterminate="false"
                        android:max="100"
                        android:progressDrawable="@drawable/bg_green_rounded" />

                </androidx.cardview.widget.CardView>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/linearText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cardView">


                    <TextView
                        android:id="@+id/txtConnecting"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_medium_500"
                        android:includeFontPadding="false"
                        android:text="@string/detecting_lock"
                        android:textColor="@color/colorAccent"
                        android:textSize="16dp"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <dots.animation.textview.TextAndAnimationView
                        android:id="@+id/animatedDots"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@+id/txtConnecting"
                        app:layout_constraintStart_toEndOf="@+id/txtConnecting"
                        app:layout_constraintTop_toTopOf="@+id/txtConnecting"
                        app:numberOfDots="3"
                        android:layout_marginStart="2dp"
                        app:setText=""
                        app:setTextSize="18" />

                    <TextView
                        android:id="@+id/tvUnlock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_medium_500"
                        android:includeFontPadding="false"
                        android:text="Tap to Unlock Door"
                        android:textColor="@color/colorAccent"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/animatedDots" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/tv_more_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:text="@string/more_info"
                    android:textColor="@color/black"
                    android:textSize="22sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtCheckIn" />

                <androidx.recyclerview.widget.RecyclerView
                    android:nestedScrollingEnabled="false"
                    android:id="@+id/rvMoreInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_more_info" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

<!--    <include-->
<!--        android:id="@+id/progressLay"-->
<!--        layout="@layout/progress_lay"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

</androidx.constraintlayout.widget.ConstraintLayout>