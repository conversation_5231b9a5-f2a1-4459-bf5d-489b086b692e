package data.network.android.models

import androidx.annotation.Keep

@Keep
class NotificationModel {
    var success: Boolean = false
    var notifcations: ArrayList<NotificationsDataModel> = ArrayList()
    var total: Int = 0
    var resultsPerPage: Int = 0
}

@Keep
class NotificationsDataModel {
    var _id: String = ""
    var notification_type: String = ""
    var user_id: String = ""
    var content: String = ""
    var image_url: String = ""
    var created_at: String = ""
    var __v: String = ""
    var status: Int = 0
}