package feature.common.compose.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.LocalContentColor
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import feature.common.compose.surface.AppRow
import keyless.feature.common.R

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    value: String,
    hint: String,
    shape: Shape = RoundedCornerShape(16.dp),
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    onValueChange: (String) -> Unit,
    leading: (@Composable () -> Unit)? = null,
    trailing: (@Composable () -> Unit)? = null
) {
    BasicTextField(
        modifier = modifier,
        value = value,
        onValueChange = onValueChange,
        keyboardActions = keyboardActions,
        keyboardOptions = keyboardOptions
    ) { textField ->
        Box(
            modifier = Modifier
                .clip(shape)
                .background(colorResource(id = R.color.bg_edit_grey))
                .padding(horizontal = 8.dp)
        ) {
            AppRow(
                arrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
                alignment = Alignment.CenterVertically
            ) {
                if (leading != null) {
                    Box { leading.invoke() }
                }

                Box(modifier = Modifier.weight(1f)) {
                    if (hint.isNotBlank() && value.isBlank()) {
                        Text(
                            text = hint,
                            color = LocalContentColor.current.copy(0.6f)
                        )
                    }
                    textField()
                }

                if (trailing != null) {
                    Box { trailing.invoke() }
                }
            }
        }
    }
}