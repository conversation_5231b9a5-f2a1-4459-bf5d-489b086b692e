name: "Release version"
description: "Generate release version"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  refName:
    required: true
    description: "Ref name"

outputs:
  versionName:
    description: "Relative file name"
    value: ${{ steps.generateReleaseVersion.outputs.versionName }}

runs:
  using: "composite"
  steps:
    - name: Generate release version
      id: generateReleaseVersion
      run: |
        RESULT=$(echo ${{ inputs.refName }} | sed 's/android.//g' | sed 's/ios.//g' | sed 's/.android//g' | sed 's/.ios//g')
        echo "versionName=$RESULT" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}