package feature.dfu.view

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.AndroidEntryPoint
import feature.dfu.data.DeviceTarget
import feature.dfu.view.dfuFirmUpdate.ProgressItemViewEntity
import feature.dfu.view.dfuFirmUpdate.WorkingProgressViewEntity
import feature.dfu.view.nearbyDfuActivity.NearByDfuDeviceActivity
import feature.dfu.viewmodel.DfuViewModel
import keyless.feature.dfu.databinding.ActivityDfuactivityBinding
import kotlinx.coroutines.launch
import java.io.File

@AndroidEntryPoint
class DFUActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDfuactivityBinding
    private lateinit var viewModel: DfuViewModel
    private var file: File? = null
    private var firmUri: Uri? = null
    private var target: DeviceTarget? = null

    val fileLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri ->
        uri?.let {

            // / this will go in eventbus subscribe
            file = feature.dfu.Utils.FileValidationUtils.getFile(baseContext, uri)
            firmUri = uri
            viewModel.onZipFileSelected(uri, this)
            deviceSelector.launch(Intent(baseContext, NearByDfuDeviceActivity::class.java).putExtra("installer", "0"))
            Log.e("TAG", ":$uri ")
            binding.tvFileName.text = "${file?.name} \n ${file?.length()}"
        }
    }

    val deviceSelector = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->

        if (result.resultCode == Activity.RESULT_OK) {
            val bleName = result.data?.getStringExtra("ble_name")
            val bleMac = result.data?.getStringExtra("ble_mac")
            Log.e("TAG Result from device select", "ForPrev: $bleName")
            binding.tvDeviceName.text = " $bleName \n $bleMac"

            target = DeviceTarget(bleName, bleMac)
            viewModel.onDeviceSelected(target!!)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDfuactivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        viewModel = ViewModelProvider(this)[DfuViewModel::class.java]

        binding.btnFileSelect.setOnClickListener {
            fileLauncher.launch("application/zip")
        }
        binding.btnUpdate.setOnClickListener {
            viewModel.onInstall()
        }
        lifecycleScope.launch {
            viewModel.state.collect { state ->

                val isRun = state.isRunning()

                showRunning(state.progressViewEntity as WorkingProgressViewEntity)

//                binding.tvProgress.text = it.toString()
            }
        }
    }

    private fun showRunning(view: WorkingProgressViewEntity) {
    }
    private fun showProgress(state: ProgressItemViewEntity) {
    }
}