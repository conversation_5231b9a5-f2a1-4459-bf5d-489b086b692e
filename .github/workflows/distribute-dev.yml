concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Distribute dev

on:
  push:
    branches:
      - dev

  workflow_dispatch:

jobs:
  check-linux:
    name: Check linux development
    uses: ./.github/workflows/reusable-check-development.yml
    secrets: inherit
    with:
      environment: "dev"
      machine: "ubuntu-22.04"

  build-android:
    name: Build release apk
    uses: ./.github/workflows/reusable-build-apk.yml
    secrets: inherit
    needs: check-linux
    with:
      apkPath:  "app/build/outputs/apk/release/app-release.apk"
      assembleVariant: "Release"
      versionName: "Dev"
      artifactUploadApkName: "built-apk"

      environment: "dev"

  distribute-android:
    name: Distribute Android
    uses: ./.github/workflows/reusable-apk-distribution-firebase.yml
    secrets: inherit
    needs: build-android
    with:
      artifactDownloadApkName: "built-apk"
      apkFullPath: ${{ needs.build-android.outputs.apkPath }}/${{ needs.build-android.outputs.apkName }}
      workflowFileName: "distribute-dev.yml"

      environment: "dev"

  remove-deployments:
    name: Remove auto deployments
    runs-on: ubuntu-22.04
    needs: distribute-android
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/deployments-delete

  cancel-check-workflow:
    name: Cancel android workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [ check-linux ]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action

  cancel-android-workflow:
    name: Cancel android workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [build-android]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action

  cancel-android-distribute-workflow:
    name: Cancel android distribute workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [distribute-android]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action