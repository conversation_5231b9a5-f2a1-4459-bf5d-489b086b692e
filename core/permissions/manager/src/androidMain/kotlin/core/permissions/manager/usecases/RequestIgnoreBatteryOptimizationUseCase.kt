package core.permissions.manager.usecases

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import core.permissions.manager.PermissionsManager
import core.permissions.manager.models.Permission
import core.permissions.manager.models.Response
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch

internal class RequestIgnoreBatteryOptimizationUseCase {

    private val observer = LifecycleEventObserver { _, event ->
        onLifecycleEvent(event)
    }
    private var isPaused = false
    private val flow = MutableSharedFlow<Unit>()

    @SuppressLint("BatteryLife")
    suspend operator fun invoke(
        owner: LifecycleOwner,
        manager: PermissionsManager
    ): List<Response> {
        val intent = Intent()
        val context = owner.getContext()

        owner.lifecycle.addObserver(observer)
        intent.action = Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
        intent.data = Uri.parse("package:" + context.packageName)
        context.startActivity(intent)

        flow.take(1).collect()
        owner.lifecycle.removeObserver(observer)

        delay(1000)
        val isGranted = manager.check(Permission.BypassBatteryRestrictions)
        val result = if (isGranted) {
            Response.Result.Granted
        } else {
            Response.Result.Denied
        }

        return listOf(
            Response(
                permission = Permission.BypassBatteryRestrictions,
                result = result
            )
        )
    }

    private
    fun onLifecycleEvent(event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_RESUME -> {
                if (isPaused) {
                    CoroutineScope(Dispatchers.Default).launch {
                        flow.emit(Unit)
                    }
                }
            }
            Lifecycle.Event.ON_PAUSE -> {
                isPaused = true
            }
            else -> {}
        }
    }

    private fun LifecycleOwner.getContext(): Context {
        return if (this is Fragment) requireContext() else this as Context
    }
}