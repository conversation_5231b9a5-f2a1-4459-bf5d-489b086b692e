package feature.common.application

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction

object App {
    var DASHBOARD_ACTIVITY_CLASS: Class<*>? = null
    var REGULA_SCANNER_ACTIVITY_CLASS: Class<*>? = null
    var LOCK_DETAILS_ACTIVITY_CLASS: Class<*>? = null

    fun addFragment(
        fragment: Fragment,
        supportFragmentManager: FragmentManager,
        layoutId: Int,
        tag: String? = null
    ) {
        if (tag == null) {
            supportFragmentManager.beginTransaction()
                .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                .add(layoutId, fragment, null).commit()
        } else {
            supportFragmentManager.beginTransaction()
                .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                .add(layoutId, fragment, null).addToBackStack(null).commit()
        }
    }
}