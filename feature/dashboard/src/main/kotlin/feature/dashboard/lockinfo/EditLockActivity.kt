package feature.dashboard.lockinfo

import android.app.Dialog
import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.Gravity
import android.view.WindowManager
import android.widget.EditText
import android.widget.PopupMenu
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.common.preferences.Preferences
import data.network.android.ApiUtils
import data.network.android.LocksListResponse
import data.network.android.models.IconModel
import data.network.android.models.ModelDataUnit
import data.network.android.models.PropertyAllResponse
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.GUEST
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.icons.SelectIconAdapter
import keyless.feature.dashboard.databinding.ActivityEditLockBinding

class EditLockActivity : AppCompatActivity(), SelectIconAdapter.SetIcon {

    private var iconId: String = ""
    private lateinit var lockDetails: LocksListResponse.LocksModel
    private var arrayIcons: ArrayList<IconModel> = ArrayList()
    lateinit var adapter: SelectIconAdapter
    private lateinit var mViewModel: EditLockViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private var buildDataId: String = ""
    private var unitDataId: String = ""
    private lateinit var popup: PopupMenu
    private lateinit var popupUnit: PopupMenu
    private lateinit var binding: ActivityEditLockBinding
    private var arrayUnit: ArrayList<ModelDataUnit> = ArrayList()
    var isClicked = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditLockBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        clickListeners()
        setAdapter()
        observerInit()
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }

        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }

//        mViewModelLockDetails.error404.observe(this) {
//            toast(it[0].lock_name)
//        }
    }

    private fun clickListeners() {
        binding.ivBack.setOnClickListener { finish() }
        binding.cvIcon.setOnClickListener {
            selectIconDialog(arrayIcons, adapter)
        }

        binding.clearUnitNumber.setOnClickListener {
            unitDataId = ""
            binding.etUnitNumber.setText("")
            binding.etApartmentNumber.setText("")
            binding.etApartmentNumber.isClickable = true
            binding.etApartmentNumber.isEnabled = true
            binding.clearUnitNumber.isVisible = false
        }

        binding.spinnerBuilding.setOnClickListener {
            if (Preferences.userRole.get() != GUEST) {
                popup.show()
            }
        }

        binding.etUnitNumber.setOnClickListener {
            if (isClicked == 0) {
                isClicked = 1
                if (arrayUnit.size > 0) {
                    isClicked = 0
                    popupUnit.show()
                } else {
                    defaultDialog(
                        this,
                        "No Units Found.",
                        object : OnActionOK {
                            override fun onClickData() {
                                isClicked = 0
                            }
                        }
                    )
                }
            }
        }

        binding.btnSaveEditDetails.setOnClickListener {
            if (validation()) {
                mViewModel.editLockApi(
                    sharePrefs.token,
                    lockDetails.lock._id,
                    buildDataId,
                    binding.etFloorNumber.text.toString(),
                    binding.etApartmentNumber.text.toString(),
                    binding.etLockName.text.toString(),
                    iconId,
                    lockDetails.property_details.map_id,
                    unitDataId
                ) { response ->
                    if (response.success) {
                        setBroadcast()
                        CommonValues.refreshApi = true
                        finish()
                    } else {
                        defaultDialog(
                            this,
                            response.message,
                            object : OnActionOK {
                                override fun onClickData() {
                                    finish()
                                }
                            }
                        )
                    }
                }
            }
        }
    }

    private fun setBroadcast() {
        lockDetails.property_details.id = buildDataId
//        //set icon
//        if (lockDetails.lock.icon.size > 0) {
//            lockDetails.lock.icon[0]._id = iconId
//        }
        // brand
        lockDetails.lock.provider = binding.etBrand.text.toString()
        // name
        lockDetails.lock.name = binding.etLockName.text.toString()
        lockDetails.unit_id = binding.etUnitNumber.text.toString()
        // building
        lockDetails.property_details.name = binding.spinnerBuilding.text.toString()
        // floor
        lockDetails.property_details.floor = binding.etFloorNumber.text.toString()
        // appartment
        lockDetails.property_details.appartment_number = binding.etApartmentNumber.text.toString()
        // room
        lockDetails.property_details.room_number = binding.etRoomNumber.text.toString()
        buildDataId = lockDetails.property_details.id
        lockDetails.lock.primary = lockDetails.lock.primary

        val extra = Intent()
            .putExtra("model", lockDetails)
        extra.action = CommonValues.LOCK_UPDATE
        sendBroadcast(extra)
    }

    private fun validation(): Boolean {
        if (iconId.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_icon))
            return false
        } else if (binding.etLockName.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_lock_name))
            return false
        } else if (buildDataId.isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_select_building_name))
            return false
        } else if (binding.etFloorNumber.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_floor_number))
            return false
        } else if (binding.etApartmentNumber.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_apartment_number))
            return false
        } else {
            return true
        }
    }

    private fun selectIconDialog(
        arrayIcons: java.util.ArrayList<IconModel>,
        adapter: SelectIconAdapter
    ) {
        val dialog = Dialog(this)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.setContentView(keyless.feature.common.R.layout.select_icon_dialog)
        dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = displayRectangle.height() * 0.9f // 60%
            val maxWidth = displayRectangle.width() * 0.9f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight.toInt())
        }

        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(true)
        dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        val rvIcon = dialog.findViewById<RecyclerView>(keyless.feature.common.R.id.rv_icon)
        val btnSave = dialog.findViewById<TextView>(keyless.feature.common.R.id.btn_save)
        val svLock = dialog.findViewById<EditText>(keyless.feature.common.R.id.sv_lock)
        rvIcon.layoutManager = GridLayoutManager(this, 3)
        rvIcon.adapter = adapter
        setUpIconRecyclerView(dialog, arrayIcons, adapter)
        btnSave.setOnClickListener {
            dialog.dismiss()
        }
        svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                adapter.filter.filter(p0.toString())
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        dialog.show()
    }

    private fun setUpIconRecyclerView(
        dialog: Dialog,
        arrayIcons: java.util.ArrayList<IconModel>,
        adapter: SelectIconAdapter
    ) {
        adapter.updateValues(arrayIcons, dialog)
    }

    private fun setAdapter() {
        adapter = SelectIconAdapter(this)
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[EditLockViewModel::class.java]
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        mViewModel.getAllUnits(sharePrefs.token, "").observe(this) {
            getUnitNumbers(it.data)
            arrayUnit = it.data
            for (i in it.data) {
                if (lockDetails.lock.unit_id == i._id) {
                    binding.etUnitNumber.setText(i.unit_number)
                    unitDataId = i._id
                    binding.clearUnitNumber.isVisible = true
                    break
                }
            }
        }

        if (Preferences.userRole.get() != GUEST) {
            mViewModel.hitGetCompanyProperty(sharePrefs.token).observe(this) {
                getBuildingSpinner(it.properties ?: arrayListOf())
            }
        } else {
            binding.etLockName.isClickable = false
            binding.etFloorNumber.isClickable = false
            binding.etLockName.inputType = InputType.TYPE_NULL
            binding.etApartmentNumber.inputType = InputType.TYPE_NULL
            binding.etFloorNumber.inputType = InputType.TYPE_NULL
            binding.etApartmentNumber.inputType = InputType.TYPE_NULL
        }

        binding.etBrand.setText(lockDetails.lock.provider)

        binding.etLockName.setText(lockDetails.lock.name)
        binding.spinnerBuilding.setText(lockDetails.property_details.name)
        buildDataId = lockDetails.property_details.id
        binding.etFloorNumber.setText(lockDetails.property_details.floor)
        binding.etApartmentNumber.setText(lockDetails.property_details.appartment_number)
        binding.etRoomNumber.setText(lockDetails.property_details.room_number)
        binding.ivIcon.setImageResource(0)
        binding.tvPress.text = getString(keyless.data.utils.android.R.string.change_icon)
        binding.textView2.text = getString(keyless.data.utils.android.R.string.edit_lock)
        if (lockDetails.lock.icon.size > 0) {
            Glide.with(this)
                .load(ApiUtils.IMAGE_BASE_URL + lockDetails.lock.icon[0].icon)
                .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                .into(binding.ivIcon)
            iconId = lockDetails.lock.icon[0]._id
        }
        mViewModel.getAllIcons(sharePrefs.token).observe(this) {
            for (i in it.icons) {
                if (i.type == "lock") {
                    arrayIcons.add(i)
                }
            }
        }
    }

    private fun getUnitNumbers(arrayList: ArrayList<ModelDataUnit>) {
        popupUnit = PopupMenu(this, binding.etUnitNumber, keyless.feature.common.R.style.PopupMenu)

        popupUnit.gravity = Gravity.START
        for (i in 0 until (arrayList.size)) {
            popupUnit.menu.add(arrayList[i].unit_number)
        }
        popupUnit.setOnMenuItemClickListener { item ->
            val _id = arrayList[arrayList.map { it.unit_number }.indexOf(item.title)]._id
            unitDataId = _id
            binding.etUnitNumber.setText(item.title)
            binding.etApartmentNumber.setText(item.title)
            binding.etApartmentNumber.isClickable = false
            binding.etApartmentNumber.isEnabled = false
            binding.clearUnitNumber.isVisible = true
            true
        }
    }

    private fun getBuildingSpinner(arrayList: java.util.ArrayList<PropertyAllResponse.Property>) {
        popup = PopupMenu(this, binding.spinnerBuilding, keyless.feature.common.R.style.PopupMenu)
        popup.gravity = Gravity.START
        for (i in 0 until (arrayList.size)) {
            popup.menu.add(arrayList[i].building_name)
        }
        popup.setOnMenuItemClickListener { item ->
            val _id =
                arrayList[arrayList.map { (it.building_name ?: "") }.indexOf(item.title)]._id!!
            buildDataId = _id
            binding.spinnerBuilding.setText(item.title)
            true
        }
    }

    override fun getIcon(iconModelAll: IconModel) {
        binding.ivIcon.setImageResource(0)
        iconId = iconModelAll._id
        lockDetails.lock.icon[0].icon = iconModelAll.icon
        lockDetails.lock.icon[0]._id = iconModelAll._id
        if (iconModelAll.icon.isNotEmpty()) {
            Glide.with(this)
                .load(ApiUtils.IMAGE_BASE_URL + iconModelAll.icon)
                .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                .into(binding.ivIcon)
        }
    }
}