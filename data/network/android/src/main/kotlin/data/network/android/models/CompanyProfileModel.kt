package data.network.android.models

import androidx.annotation.Keep

@Keep
data class CompanyProfileResponse(
    val success: Boolean? = null,
    val data: Data? = null
)

@Keep
data class Data(
    val _id: String? = null,
    val status: Long? = null,
    val country_code: String? = null,
    val timezone: String = "",
    val timezone_name: String = "",
    val company_name: String? = null,
    val business_lia: String = "",
    val url: String? = null,
    val first_name: String? = null,
    val last_name: String? = null,
    val address: String? = null,
    val city: String? = null,
    val email_id: String? = null,
    val mobile_number: Long? = null,
    val user_id: String? = null,
    val total_licenses: Long? = null,
    val card_details: String? = null,
    val created_at: String? = null,
    val __v: Long? = null,
    val updated_at: String? = null,
    val plan_id: String? = null,
    val trn_number: String = "",
    val business_type: String = "",
    val trade_license_number: String = "",
    val country: String? = null,
    val zip_code: String? = null,
    val checkin: Boolean = false
)

@Keep
data class UpdateCompanyProfileResponse(
    val success: Boolean? = null,
    val message: String? = null
)