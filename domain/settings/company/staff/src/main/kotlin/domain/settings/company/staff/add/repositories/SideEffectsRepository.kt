package domain.settings.company.staff.add.repositories

import domain.settings.company.staff.add.models.SideEffect
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

class SideEffectsRepository {
    private val state = MutableSharedFlow<SideEffect>()
    val stream = state.asSharedFlow()

    suspend fun emit(sideEffect: SideEffect) {
        state.emit(sideEffect)
    }
}