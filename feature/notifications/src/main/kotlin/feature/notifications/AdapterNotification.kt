package feature.notifications

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import data.network.android.models.NotificationsDataModel
import data.utils.android.CommonValues
import keyless.feature.notifications.databinding.NotificationAdapterLayBinding

class AdapterNotification(context: Context) :
    RecyclerView.Adapter<AdapterNotification.ViewHolder>() {

    lateinit var context: Context
    var listMain: ArrayList<NotificationsDataModel> = ArrayList()

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = NotificationAdapterLayBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("NewA<PERSON>", "SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.N)
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listMain[position])
    }

    override fun getItemCount() = listMain.size
    fun updateData(users: ArrayList<NotificationsDataModel>) {
        listMain = users
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: NotificationAdapterLayBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: NotificationsDataModel) {
            binding.notificationTxt.text = model.content
            binding.dateNotification.text = CommonValues.formateTimeDate(
                model.created_at.split(
                    "."
                )[0], context
            )

            if (model.status == 1) {
                val typeface1: Typeface = context.resources.getFont(keyless.data.utils.android.R.font.poppins_bold_700)
                // or to support all versions use
                // or to support all versions use
                val typeface: Typeface? = ResourcesCompat.getFont(
                    context,
                    keyless.data.utils.android.R.font.poppins_bold_700
                )
                binding.notificationTxt.typeface = typeface
            } else {
                val typeface: Typeface? = ResourcesCompat.getFont(
                    context,
                    keyless.data.utils.android.R.font.poppins_medium_500
                )
                binding.notificationTxt.typeface = typeface
            }
        }
    }
}