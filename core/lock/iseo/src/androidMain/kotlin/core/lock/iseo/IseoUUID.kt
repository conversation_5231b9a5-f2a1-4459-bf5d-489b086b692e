package core.lock.iseo

import com.iseo.v364droidsdk.service.mobilecredentialservice.DroidMobileCredentialServiceFactory
import com.iseo.v364droidsdk.service.scanservice.DroidScanManagerServiceFactory

fun getUUID(): String {
    val manager = DroidScanManagerServiceFactory()
    val scan = manager.createScanManager()
    val credentials = DroidMobileCredentialServiceFactory(scan).createMobileCredentialService()

    return credentials.userIdentity.mobileCredentialUUID
}