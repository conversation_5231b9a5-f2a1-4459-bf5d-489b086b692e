package domain.settings.profile.models

sealed interface Event

sealed interface EmailEvent : Event {

    class SendOtp(
        val email: String
    ) : EmailEvent

    class VerifyEmail(
        val email: String,
        val otp: String,
        val deviceType: String,
        val userId: String
    ) : EmailEvent
}

sealed interface ProfileEvent : Event {

    class ChangePassword(
        val oldPassword: String,
        val newPassword: String,
        val confirmPassword: String,
        val userId: String,
        val isAdmin: Boolean
    ) : ProfileEvent

    class UpdateProfile(
        val firstName: String,
        val lastName: String
    ) : ProfileEvent

    object GetUserProfile : ProfileEvent

    object DeleteAccount : ProfileEvent
}

sealed interface ScreenEvent : Event {

    class UpdateError(
        val error: String
    ) : ScreenEvent
}