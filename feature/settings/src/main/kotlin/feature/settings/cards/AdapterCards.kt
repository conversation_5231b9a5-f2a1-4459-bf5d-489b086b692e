package feature.settings.cards

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.network.android.DataModelCard
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.feature.settings.databinding.CardAdapterLayoutBinding

class AdapterCards(contextMain: Context) :
    RecyclerView.Adapter<AdapterCards.ViewHolder>() {

    lateinit var context: Context
    var listNew = ArrayList<DataModelCard>()
    var listener = contextMain as SelectCard

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = CardAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(listNew[position])
    }

    override fun getItemCount() = listNew.size

    fun update(it: ArrayList<DataModelCard>) {
        listNew = it
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: CardAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: DataModelCard) {
            binding.txtInternalId.text = model.internal_id
            binding.txtCardId.text = CommonValues.formateTimeDate(model.created_at,context)
            if (model.lock_assign.size > 0) {
                binding.txtStatus.text = "Assigned"
                binding.txtStatus.setTextColor(context.getColor(keyless.feature.common.R.color.red2))
            } else {
                binding.txtStatus.text = "Available"
                binding.txtStatus.setTextColor(context.getColor(keyless.feature.common.R.color.green))
            }

            binding.mainLay.setOnClickListener {
                if (model.lock_assign.size == 0) {
                    listener.clickOnItem(model)
                } else {
                    defaultDialog(
                        context,
                        "This card is already assigned to lock. Please select another card.",
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        }
                    )
                }
            }
        }
    }

    interface SelectCard {

        fun clickOnItem(modelCards: DataModelCard)
    }
}