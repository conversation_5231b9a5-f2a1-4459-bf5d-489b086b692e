package domain.settings.checkin.repositories

import domain.settings.checkin.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

class ScreenStateRepository {
    private val state = MutableStateFlow(emptyState())
    val stream = combination(
        screenState = state
    ) { screen ->
        screen.copy()
    }

    fun update(block: (ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}