package presentation.home.dashboard.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import keyless.presentation.common.R
import kotlinx.coroutines.flow.flowOf
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppBottomSheet
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppHorizontalDivider
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppTextButton
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.AppVerticalDivider
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState
import presentation.common.feature.theme.AppTheme
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.UserEvent

@Composable
internal fun ClaimYourKeyDialog(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onDismiss: () -> Unit = { state.ui.claimKey.update(ClaimKeyState.None) }
) {
    val state = remember { state.ui.claimKey.get() as ClaimKeyState.Show }
    Dialog(onDismissRequest = onDismiss) {
        ClaimYourKeyDialogContent(
            state = state,
            onConfirm = { onEvent(UserEvent.ClaimKey(state.retrievalCode.get())) },
            onDismiss = onDismiss
        )
    }
}

@Composable
private fun ClaimYourKeyDialogContent(
    state: ClaimKeyState.Show,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(DesignSystem.Corner.medium))
            .background(Color.White)
            .padding(DesignSystem.Padding.screen),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        ClaimYourKeyDialogTitle()

        ClaimYourKeyDialogDescription()

        ClaimYourKeyDialogTextField(state = state.retrievalCode)

        AppHorizontalDivider()

        ClaimYourKeyDialogButtons(onConfirm = onConfirm, onDismiss = onDismiss)
    }
}

@Composable
private fun ClaimYourKeyDialogTitle() {
    AppPageTitleText(
        modifier = Modifier.padding(top = DesignSystem.Padding.large),
        text = stringResource(R.string.claim_your_key),
        design = DesignSystem.Text.PageTitle.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun ClaimYourKeyDialogDescription() {
    AppBodyText(
        modifier = Modifier.padding(horizontal = DesignSystem.Padding.large),
        text = stringResource(R.string.please_enter_your_retrieval_code_to_claim_your_key),
        design = DesignSystem.Text.Body.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun ClaimYourKeyDialogTextField(state: StringState) {
    AppTextField(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.medium),
        state = state,
        hint = stringResource(R.string.enter_retrieval_code_number),
        singleLine = true
    )
}

@Composable
private fun ClaimYourKeyDialogButtons(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AppRow(
        modifier = Modifier.fillMaxWidth().height(IntrinsicSize.Min),
        alignment = Alignment.CenterVertically
    ) {
        ClaimYourKeyDialogCancelButton(onDismiss)

        AppVerticalDivider()

        ClaimYourKeyDialogProceedButton(onConfirm)
    }
}

@Composable
private fun RowScope.ClaimYourKeyDialogProceedButton(onConfirm: () -> Unit) {
    AppTextButton(
        modifier = Modifier.weight(1f),
        text = stringResource(R.string.proceed),
        onClick = onConfirm
    )
}

@Composable
private fun RowScope.ClaimYourKeyDialogCancelButton(onDismiss: () -> Unit) {
    AppTextButton(
        modifier = Modifier.weight(1f),
        text = stringResource(R.string.text_cancel),
        onClick = onDismiss
    )
}

@Composable
internal fun GroceriesDialog(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onDismiss: () -> Unit = { state.ui.groceriesDialog.update(GroceriesDialogState.None) }
) {
    val dialogState = remember { state.ui.groceriesDialog.get() as GroceriesDialogState.Show }
    Dialog(onDismissRequest = onDismiss) {
        GroceriesDialogContent(
            type = dialogState.type,
            onEvent = {
                onEvent(it)
                onDismiss()
            },
            onDismiss = onDismiss
        )
    }
}

@Composable
private fun GroceriesDialogContent(
    type: GroceriesDialogState.Type,
    onEvent: (Event) -> Unit,
    onDismiss: () -> Unit
) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(DesignSystem.Corner.medium))
            .background(Color.White)
            .padding(DesignSystem.Padding.screen),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        GroceriesDialogTitle(type)

        GroceriesDialogItems(type, onEvent)

        GroceriesDialogCancelButton(onDismiss)
    }
}

@Composable
private fun GroceriesDialogItems(
    type: GroceriesDialogState.Type,
    onEvent: (Event) -> Unit
) {
    if (type == GroceriesDialogState.Type.Groceries) {
        GroceriesDialogGroceriesItems(onEvent)
    } else {
        GroceriesDialogTaxiItems(onEvent)
    }
}

@Composable
private fun GroceriesDialogTitle(type: GroceriesDialogState.Type) {
    val res = remember(type) { if (type == GroceriesDialogState.Type.Groceries) R.string.groceries else R.string.taxi }
    AppPageTitleText(
        modifier = Modifier.padding(top = DesignSystem.Padding.large),
        text = stringResource(res),
        design = DesignSystem.Text.PageTitle.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun GroceriesDialogGroceriesItems(onEvent: (Event) -> Unit) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        GroceriesDialogItemInstashop(onEvent)
        GroceriesDialogItemNoonDaily(onEvent)
    }
}

@Composable
private fun GroceriesDialogTaxiItems(onEvent: (Event) -> Unit) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        GroceriesDialogItemXXRide(onEvent)
        GroceriesDialogItemHalaTaxi(onEvent)
    }
}

@Composable
private fun GroceriesDialogItem(imgRes: Int, textRes: Int, onClick: () -> Unit) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, MaterialTheme.shapes.medium)
            .padding(DesignSystem.Padding.medium)
            .hiddenClickable { onClick() },
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large, Alignment.CenterHorizontally),
        alignment = Alignment.CenterVertically
    ) {
        GroceriesDialogItemIcon(imgRes)

        GroceriesDialogItemText(textRes)
    }
}

@Composable
private fun GroceriesDialogItemText(res: Int) {
    AppBodyText(text = stringResource(res))
}

@Composable
private fun GroceriesDialogItemIcon(res: Int) {
    AppImage(res = res, modifier = Modifier.size(24.dp))
}

@Composable
private fun GroceriesDialogCancelButton(onDismiss: () -> Unit) {
    AppTextButton(
        modifier = Modifier.fillMaxWidth(),
        text = stringResource(R.string.text_cancel),
        onClick = onDismiss
    )
}

@Composable
private fun GroceriesDialogItemInstashop(onEvent: (Event) -> Unit) {
    GroceriesDialogItem(
        textRes = R.string.instashop,
        imgRes = R.drawable.img_insta_shop,
        onClick = {
            onEvent(UserEvent.LogService("groceries/instashop"))
            onEvent(UserEvent.OpenUrl("https://instashop.com/"))
        }
    )
}

@Composable
private fun GroceriesDialogItemNoonDaily(onEvent: (Event) -> Unit) {
    GroceriesDialogItem(
        textRes = R.string.noon_daily,
        imgRes = R.drawable.img_noon_daily,
        onClick = {
            onEvent(UserEvent.LogService("groceries/noon_daily"))
            onEvent(UserEvent.OpenUrl("https://daily.noon.com/uae-en/"))
        }
    )
}

@Composable
private fun GroceriesDialogItemXXRide(onEvent: (Event) -> Unit) {
    GroceriesDialogItem(
        textRes = R.string.xxride,
        imgRes = R.drawable.img_xxride,
        onClick = {
            onEvent(UserEvent.LogService("taxi/xxride"))
            onEvent(UserEvent.OpenUrl("https://play.google.com/store/apps/details?id=com.app.xxrideuser&hl=en_US"))
        }
    )
}

@Composable
private fun GroceriesDialogItemHalaTaxi(onEvent: (Event) -> Unit) {
    GroceriesDialogItem(
        textRes = R.string.hala_taxi,
        imgRes = R.drawable.img_hala,
        onClick = {
            onEvent(UserEvent.LogService("taxi/hala_taxi"))
            onEvent(
                UserEvent.OpenUrl(
                    "https://play.google.com/store/apps/details?id=com.careem.acma&referrer=" +
                            "adjust_reftag%3Dc064DQqtCdC4i%26utm_source%3DIVR%2BSMS%26utm_campaign%3DSira"
                )
            )
        }
    )
}

@Composable
private fun CallSheet(state: StateHolder, onEvent: (Event) -> Unit, onDismiss: () -> Unit = {}) {
    val state = remember { state.ui.callBottomSheet.get() as CallBottomSheetState.Show }
    AppBottomSheet(
        onDismiss = onDismiss,
        title = state.title
    ) {

    }
}

@Preview
@Composable
private fun Preview() {
    AppTheme { GroceriesDialog(StateHolder(data = DerivedState(ScreenData.empty, flowOf())), onEvent = {}) }
}