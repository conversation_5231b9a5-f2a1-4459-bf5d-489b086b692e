<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iconProperty"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_margin="15dp"
        android:src="@drawable/iv_other_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/placeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/mirrorText"
        android:layout_marginStart="15dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintStart_toEndOf="@+id/iconProperty"
        app:layout_constraintTop_toTopOf="@+id/iconProperty" />

    <ImageView
        android:id="@+id/imageView8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:src="@drawable/marker"
        app:layout_constraintStart_toStartOf="@+id/placeName"
        app:layout_constraintTop_toBottomOf="@+id/placeName" />

    <TextView
        android:layout_marginEnd="20dp"
        android:id="@+id/addressMarker"
        android:layout_width="0dp"
        style="@style/mirrorText"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:fontFamily="@font/poppins_regular_400"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/imageView8"
        app:layout_constraintTop_toBottomOf="@+id/placeName" />

    <TextView
        android:id="@+id/textView13"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/no_of_floor"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/iconProperty"
        app:layout_constraintTop_toBottomOf="@+id/addressMarker"
        app:layout_goneMarginTop="25dp" />

    <TextView
        android:id="@+id/noFloorTxt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="8dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline5"
        app:layout_constraintStart_toEndOf="@+id/textView13"
        app:layout_constraintTop_toTopOf="@+id/textView13" />

    <TextView
        android:id="@+id/textView14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/locks"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline5"
        app:layout_constraintTop_toTopOf="@+id/textView13"
        app:layout_goneMarginTop="25dp" />

    <TextView
        android:id="@+id/noLockTxt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/textView14"
        app:layout_constraintTop_toTopOf="@+id/textView14" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.6" />


</androidx.constraintlayout.widget.ConstraintLayout>