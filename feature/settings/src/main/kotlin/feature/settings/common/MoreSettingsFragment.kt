package feature.settings.common

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.os.AsyncTask
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.khoiron.actionsheets.ActionSheet
import com.khoiron.actionsheets.callback.ActionSheetCallBack
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADMIN
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.CommonValues.Companion.INTEGRATOR
import data.utils.android.applications.MyApp
import data.utils.android.database.Constant
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.defaultDialog
import feature.common.dialogs.dialogYesNo
import data.utils.android.interfaces.ClickFragments
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.application.App.DASHBOARD_ACTIVITY_CLASS
import feature.common.dialogs.ProgressDialogUtils
import feature.settings.aboutus.AboutUsNewActivity
import feature.settings.admin.SelectUserAdminActivity
import feature.settings.cards.ConfiguredCardsActivity
import feature.settings.company.CompanyProfileActivity
import feature.settings.company.staff.ManageStaffActivity
import feature.settings.maintenance.MaintenanceActivity
import feature.settings.checkin.CheckInPMActivity
import feature.settings.profile.ProfileActivity
import keyless.data.utils.android.R
import keyless.feature.settings.databinding.FragmentMoreSettingsBinding

class MoreSettingsFragment : Fragment() {

    lateinit var viewModel: CommonHomeSettingsViewModel
    lateinit var updateValue: ClickFragments
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    private lateinit var binding: FragmentMoreSettingsBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMoreSettingsBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        checkGuest()
        clickListeners()
        observerInit()
    }

    private fun checkGuest() {
        if (Preferences.userRole.get() == GUEST) {
            binding.fullGroup.visibility = GONE
            binding.fullGroupMaintenance.visibility = GONE
            binding.moreTitle.text = getString(keyless.data.utils.android.R.string.text_settings)
            binding.imageView12.isVisible = true

            binding.checkInViewBtn.isVisible = false
            binding.viewCheckIn.isVisible = false
            binding.txtCheckIn.isVisible = false

            binding.signInView.isVisible = false
            binding.viewSignIn.isVisible = false
            binding.signInAdmin.isVisible = false
        } else if (Preferences.userRole.get() == INTEGRATOR) {
            binding.txtValue10.isVisible = true
            binding.view12.isVisible = true
            binding.manageCardsView.isVisible = true

            binding.imageView12.isVisible = false
            binding.signInView.isVisible = false
            binding.viewSignIn.isVisible = false
            binding.signInAdmin.isVisible = false
            if (
                Preferences.role.get() == ADMIN ||
                Preferences.role.get() == CommonValues.OWNER ||
                Preferences.role.get() == Roles.SYSTEM_MANAGER
            ) {
                binding.checkInViewBtn.isVisible = true
                binding.viewCheckIn.isVisible = true
                binding.txtCheckIn.isVisible = true
            } else {
                binding.checkInViewBtn.isVisible = false
                binding.viewCheckIn.isVisible = false
                binding.txtCheckIn.isVisible = false
            }
            binding.fullGroupMaintenance.isVisible =
                Preferences.role.get() == ADMIN || Preferences.role.get() == CommonValues.OWNER
        } else if (Preferences.userRole.get() == ADMIN) {
            binding.fullGroup.visibility = GONE
            binding.fullGroupMaintenance.visibility = GONE
            binding.manageCardsView.isVisible = false
            binding.snableScreenLockBtn.isVisible = false
            binding.view5.isVisible = false
            binding.simpleSwitch.isVisible = false
            binding.txtValue5.isVisible = false
            binding.txtValue6.isVisible = false
            binding.view6.isVisible = false
            binding.supportBtn.isVisible = false
            binding.txtValue7.isVisible = false
            binding.view7.isVisible = false
            binding.aboutUsBtn.isVisible = false
            binding.signInView.isVisible = true
            binding.viewSignIn.isVisible = true
            binding.signInAdmin.isVisible = true
            binding.myProfileBtn.isVisible = false
            binding.view1.isVisible = false
            binding.txtValue.isVisible = false
            binding.imageView12.isVisible = false

            binding.checkInViewBtn.isVisible = false
            binding.viewCheckIn.isVisible = false
            binding.txtCheckIn.isVisible = false
        }

        if (!Preferences.isPaidUser.get()) {
            binding.manageStaffBtn.isVisible = false
            binding.txtValue3.isVisible = false
            binding.view3.isVisible = false
        }

        if (sharePrefs.language == "en") {
            binding.selectedLanguage.text = getString(keyless.data.utils.android.R.string.english)
        } else if (sharePrefs.language == "ar") {
            binding.selectedLanguage.text = getString(keyless.data.utils.android.R.string.arabic)
        }

        if (Preferences.isAdminLogin()) {
            binding.textView28.isVisible = false
            binding.logout.isVisible = false
        } else {
            binding.textView28.isVisible = true
            binding.logout.isVisible = true
        }
    }

    private fun observerInit() {
        viewModel.getResponseLogout.observe(viewLifecycleOwner) {
            deleteTable()
            updateValue.clicksIntents(1)
        }

        viewModel.progress.observe(viewLifecycleOwner) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        viewModel.error.observe(viewLifecycleOwner) {
            requireContext().toast(it)
        }
    }

    private fun deleteTable() {
        class DeleteTask : AsyncTask<Void, Void, Void>() {
            override fun doInBackground(vararg params: Void?): Void? {
                val db = Constant.getDataBase(activity!!)
                try {
                    db!!.daoAccess().deleteAllData()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                return null
            }
        }
        DeleteTask().execute()
    }

    private fun initz() {
        binding.simpleSwitch.isChecked =
            SharedPreferenceUtils.getInstance(requireActivity()).isScreenLockEnabled
        viewModel = ViewModelProvider(this)[CommonHomeSettingsViewModel::class.java]
        binding.versionName.text = getString(keyless.data.utils.android.R.string.version) +
                " " + MyApp.AppVersion
    }

    private fun isDevicePassProtected(): Boolean {
        val myKM = requireActivity().getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        if (myKM.isKeyguardSecure) {
            // it is locked
            return true
        }
        return false
    }

    private fun clickListeners() {
        // profile
        binding.checkInViewBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), CheckInPMActivity::class.java))
        }

        binding.myProfileBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), ProfileActivity::class.java))
        }

        binding.imageView12.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        // Company name
        binding.companyProfileBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), CompanyProfileActivity::class.java))
        }

        // Manage staff
        binding.manageStaffBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), ManageStaffActivity::class.java))
        }

        // Enable screen Lock
        binding.simpleSwitch.setOnCheckedChangeListener { compoundButton, checked ->
            if (Preferences.isAdminLogin()) {
                binding.simpleSwitch.isChecked = false
                defaultDialog(
                    requireActivity(),
                    getString(R.string.disabled_in_admin_mode),
                    object : OnActionOK {
                        override fun onClickData() = Unit
                    }
                )
            } else {
                SharedPreferenceUtils.getInstance(requireActivity()).isScreenLockEnabled = checked
                if (checked) {
                    val devicePassProtected = isDevicePassProtected()
                    if (!devicePassProtected) {
                        requireActivity().toast(
                            getString(keyless.data.utils.android.R.string.this_device_has_no_security)
                        )
                        binding.simpleSwitch.isChecked = !binding.simpleSwitch.isChecked
                        SharedPreferenceUtils.getInstance(requireActivity()).isScreenLockEnabled = false
                    }
                }
            }
        }

        // Support
        binding.supportBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), SupportActivity::class.java))
        }

        // About us
        binding.aboutUsBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), AboutUsNewActivity::class.java))
        }

        // Logout
        binding.logout.setOnClickListener {
            dialogYesNo(
                requireActivity(),
                getString(R.string.logout),
                getString(R.string.are_you_sure_you_want_to_logout),
                object : OnActionYesNo {
                    override fun onYes(view: View) {
                        viewModel.logoutAccount(sharePrefs.token, requireActivity())
                    }

                    override fun onNo(view: View) {
                    }

                    override fun onClickData(view: View, data: String) {
                    }
                }
            )
        }

        binding.viewLanguage.setOnClickListener {
            val listLanguage = ArrayList<String>()
            listLanguage.add(getString(keyless.data.utils.android.R.string.txt_english))
            listLanguage.add(getString(keyless.data.utils.android.R.string.text_arabic))
            ActionSheet(requireActivity(), listLanguage)
                .setTitle(requireActivity().getString(keyless.data.utils.android.R.string.change_language))
                .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
                .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .create(object : ActionSheetCallBack {
                    override fun data(data: String, position: Int) {
                        val config = resources.configuration
                        if (data == "English") {
                            sharePrefs.language = "en"
//                            requireActivity().window.decorView.layoutDirection = View.LAYOUT_DIRECTION_LTR
                        } else if (data == "Arabic") {
                            sharePrefs.language = "ar"
//                            requireActivity().window.decorView.layoutDirection = View.LAYOUT_DIRECTION_RTL
                        }
                        startActivity(
                            Intent(requireActivity(), DASHBOARD_ACTIVITY_CLASS).putExtra("runSplash", false)
                        )
                        requireActivity().finish()
//                        updateValue.clicksIntents(4)
                    }
                })
        }

        binding.manageCardsView.setOnClickListener {
            startActivity(Intent(requireActivity(), ConfiguredCardsActivity::class.java))
        }

        binding.maintenanceStaffBtn.setOnClickListener {
            startActivity(Intent(requireActivity(), MaintenanceActivity::class.java))
        }

        binding.signInView.setOnClickListener {
            val data: ArrayList<String> = ArrayList()
            data.add(getString(keyless.data.utils.android.R.string.project_manager))
            data.add(getString(keyless.data.utils.android.R.string.guest))
            ActionSheet(requireActivity(), data)
                .setTitle(getString(keyless.data.utils.android.R.string.select_user_type))
                .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
                .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .create(object : ActionSheetCallBack {
                    override fun data(data: String, position: Int) {
                        when (position) {
                            0 -> {
                                startActivity(
                                    Intent(requireActivity(), SelectUserAdminActivity::class.java).putExtra("type", "0")
                                )
                            }

                            1 -> {
                                startActivity(
                                    Intent(requireActivity(), SelectUserAdminActivity::class.java).putExtra("type", "1")
                                )
                            }
                        }
                    }
                })
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateValue = context as ClickFragments
    }

    override fun onAttach(activity: Activity) {
        super.onAttach(activity)
        updateValue = activity as ClickFragments
    }
}