package feature.regula

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import feature.regula.Helpers.Companion.PERMISSIONS_REQUEST_READ_EXTERNAL_STORAGE
import feature.regula.Helpers.Companion.getBitmap
import com.regula.documentreader.api.DocumentReader.Instance
import com.regula.documentreader.api.completions.IDocumentReaderCompletion
import com.regula.documentreader.api.completions.IDocumentReaderPrepareCompletion
import com.regula.documentreader.api.config.RecognizeConfig
import com.regula.documentreader.api.enums.DocReaderAction
import com.regula.documentreader.api.errors.DocumentReaderException
import com.regula.documentreader.api.params.DocReaderConfig
import com.regula.documentreader.api.results.DocumentReaderResults
import data.network.android.LocksListResponse
import keyless.feature.regula.R
import keyless.feature.regula.databinding.ScanActivityBinding
import java.io.Serializable
import java.util.*

class ScanActivity : FragmentActivity(), Serializable {
    private var mTimerAnimator: ValueAnimator? = null
    private var isAnimationStarted: Boolean = false
    private var currentScenario: String = ""

    @Transient
    private lateinit var binding: ScanActivityBinding

    @Transient
    private var loadingDialog: AlertDialog? = null

    @Transient
    private var initDialog: AlertDialog? = null

    @SuppressLint("WrongConstant")
    @Transient
    val imageBrowsingIntentLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                it.data?.let { intent ->
                    val imageUris = ArrayList<Uri>()
                    if (intent.clipData == null) {
                        intent.data?.let { uri ->
                            imageUris.add(uri)
                        }
                    } else {
                        intent.clipData?.let { clipData ->
                            for (i in 0 until clipData.itemCount) {
                                imageUris.add(clipData.getItemAt(i).uri)
                            }
                        }
                    }
                    if (imageUris.size > 0) {
                        loadingDialog = showDialog("Processing image")
                        if (imageUris.size == 1) {
                            getBitmap(imageUris[0], 1920, 1080, this)?.let { bitmap ->
                                val recognizeConfig =
                                    RecognizeConfig.Builder(currentScenario).setBitmap(bitmap)
                                        .build()
                                Instance().recognize(recognizeConfig, completion)
                            }
                        } else {
                            val bitmaps = arrayOfNulls<Bitmap>(imageUris.size)
                            for (i in bitmaps.indices) {
                                bitmaps[i] = getBitmap(imageUris[i], 1920, 1080, this)
                            }
                            val recognizeConfig =
                                RecognizeConfig.Builder(currentScenario).setBitmaps(bitmaps).build()
                            Instance().recognize(recognizeConfig, completion)
                        }
                    }
                }
            }
        }

    @Transient
    val customRfidIntentLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            results?.let { displayResults(it) }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (LicenseUtil.readFileFromAssets(this) == null &&
            !isInitializedByBleDevice
        ) {
            showDialog(
                this@ScanActivity
            )
        }
        binding = ScanActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        Helpers.opaqueStatusBar(binding.root)
        if (Instance().isReady) {
            onInitComplete()
        }
    }

    override fun onResume() {
        super.onResume()
        if (Instance().isReady) {
            return
        }

        val license = LicenseUtil.readFileFromAssets(
            this
        )

        license?.let {
            initDialog = showDialog(getString(keyless.data.utils.android.R.string.please_wait))
            getPrepareCompletion(it)
        }?.let {
            Instance().prepareDatabase(this, "Full", it)
        }
    }

    private fun getPrepareCompletion(license: ByteArray) =
        object : IDocumentReaderPrepareCompletion {
            override fun onPrepareProgressChanged(progress: Int) {
                initDialog?.setTitle("Downloading database: $progress%")
            }

            override fun onPrepareCompleted(status: Boolean, error: DocumentReaderException?) {
                Instance()
                    .initializeReader(this@ScanActivity, DocReaderConfig(license)) { success, error_initializeReader ->
                        if (initDialog?.isShowing == true) {
                            initDialog?.dismiss()
                        }
                        Instance().customization().edit().setShowHelpAnimation(false).apply()

                        if (success) {
                            onInitComplete()
                        } else {
                            Toast.makeText(
                                this@ScanActivity,
                                "Init failed:$error_initializeReader",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
            }
        }

    private fun onInitComplete() {
        currentScenario = Instance().processParams().scenario
        val scenarios: Array<String?> = arrayOfNulls(Instance().availableScenarios.size)
        val newScenarios: ArrayList<String?> = ArrayList()
        for ((i, scenario) in Instance().availableScenarios.withIndex())
            scenarios[i] = scenario.name

        newScenarios.add("FullProcess")
        if (scenarios.isNotEmpty()) {
            if (currentScenario.isEmpty()) {
                currentScenario = newScenarios[0] ?: ""
            }

            showScanner()
        } else {
            Toast.makeText(
                this@ScanActivity,
                "Available scenarios list is empty",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    override fun onPause() {
        super.onPause()
        hideDialog()
    }

    private fun showDialog(msg: String): AlertDialog {
        val dialog = AlertDialog.Builder(this)
        dialog.setTitle(msg)
        dialog.setView(layoutInflater.inflate(R.layout.simple_dialog, binding.root, false))
        dialog.setCancelable(false)
        return dialog.show()
    }

    private fun hideDialog() {
        loadingDialog?.dismiss()
        loadingDialog = null
    }

    @Transient
    private val completion = IDocumentReaderCompletion { action, results, error ->
        if (!isAnimationStarted) {
            mTimerAnimator?.let {
                it.start()
                isAnimationStarted = true
            }
        }
        if (action == DocReaderAction.COMPLETE ||
            action == DocReaderAction.TIMEOUT
        ) {
            hideDialog()
            cancelAnimation()
            if (Instance().functionality().isManualMultipageMode) {
                if (results?.morePagesAvailable != 0) {
                    Instance().startNewPage()
                    Handler(Looper.getMainLooper()).postDelayed({
                        showScanner()
                    }, 100)
                    return@IDocumentReaderCompletion
                } else {
                    Instance().functionality().edit().setManualMultipageMode(false).apply()
                }
            }
            displayResults(results!!)
        } else {
            if (action == DocReaderAction.CANCEL) {
                if (Instance().functionality().isManualMultipageMode) {
                    Instance().functionality().edit().setManualMultipageMode(false).apply()
                }

                Toast.makeText(this, "Scanning cancelled", Toast.LENGTH_LONG).show()
                hideDialog()
                cancelAnimation()
            } else if (action == DocReaderAction.ERROR) {
                Toast.makeText(this, "Error:$error", Toast.LENGTH_LONG).show()
                hideDialog()
                cancelAnimation()
            }
        }
    }

    @SuppressLint("SuspiciousIndentation")
    private fun displayResults(documentReaderResults: DocumentReaderResults) {
        ResultsActivity.results = documentReaderResults
//        if (isDataEncryptionEnabled) {
//            val input = JSONObject(ResultsActivity.results.rawResult)
//            val processParam = JSONObject()
//                .put("alreadyCropped", true)
//                .put("scenario", "FullProcess")
//            val output = JSONObject()
//                .put("List", input.getJSONObject("ContainerList").getJSONArray("List"))
//                .put("processParam", processParam)
//            postRequest(output.toString())
//        } else
        startActivity(Intent(this, ResultsActivity::class.java))
    }

    @SuppressLint("WrongConstant")
    fun showScanner() {
//        val scannerConfig = ScannerConfig.Builder(currentScenario).build()
//        val scannerConfig = ScannerConfig.Builder("FullProcess").build()
//        Instance().showScanner(this@ScanActivity, scannerConfig, completion)
        val lockDetails = intent.getParcelableExtra<LocksListResponse.LocksModel>("lockDetails")

        val intent = Intent(this, DocScanActivity::class.java).putExtra("lockDetails", lockDetails)
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivityForResult(intent, 60)

//        startActivityForResult(Intent(this,DocScanActivity::class.java).putExtra("lockDetails",lockDetails),60)
//        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 70) {
            setResult(80)
            finish()
        } else if (resultCode == 400) {
            finish()
        }
    }

    private fun createImageBrowsingRequest() {
        val intent = Intent()
        intent.type = "image/*"
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        intent.action = Intent.ACTION_GET_CONTENT
        imageBrowsingIntentLauncher.launch(Intent.createChooser(intent, "Select Picture"))
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PERMISSIONS_REQUEST_READ_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    createImageBrowsingRequest()
                } else {
                    Toast.makeText(this, "Permission required, to browse images", Toast.LENGTH_LONG)
                        .show()
                }
            }
        }
    }

    private fun showDialog(context: Context) {
        AlertDialog.Builder(context)
            .setTitle("Error")
            .setMessage("license in assets is missed")
            .setPositiveButton("Close") { dialog, which ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    private fun cancelAnimation() {
        mTimerAnimator?.let {
            it.cancel()
            isAnimationStarted = false
            mTimerAnimator = null
        }
        Instance().customization().edit().setUiCustomizationLayer(null).apply()
    }

    companion object {
        var results: DocumentReaderResults? = null
        var isInitializedByBleDevice: Boolean = false
        const val ENCRYPTED_RESULT_SERVICE = "https://api.regulaforensics.com/api/process"
    }
}