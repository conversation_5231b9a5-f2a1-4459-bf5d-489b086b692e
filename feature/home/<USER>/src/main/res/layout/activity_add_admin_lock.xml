<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context=".dashboard.home.adminHome.AddAdminLockActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/iv_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewBack"
                android:layout_width="30dp"
                android:layout_height="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/add_lock"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar2"
        app:layout_constraintVertical_bias="0.0">


        <TextView
            android:id="@+id/tv_brand"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/brand"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/et_brand"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:editable="false"
            style="@style/mirrorText"
            android:fontFamily="@font/poppins_medium_500"
            android:maxLines="1"
            android:hint="@string/brand"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:text="@string/text_keyless"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_brand"
            tools:ignore="Deprecated" />

        <TextView
            android:id="@+id/tv_lock_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/lock_model"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/et_brand" />

        <EditText
            android:id="@+id/txtLockModel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/lock_model"
            style="@style/mirrorText"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:singleLine="true"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_lock_name" />

        <View
            android:id="@+id/viewScanning"
            android:layout_width="70dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/txtLockModel"
            app:layout_constraintEnd_toEndOf="@+id/txtLockModel"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="@+id/guideline3"
            app:layout_constraintTop_toTopOf="@+id/txtLockModel" />


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:src="@drawable/iv_scan_add_new"
            app:layout_constraintBottom_toBottomOf="@+id/txtLockModel"
            app:layout_constraintEnd_toEndOf="@+id/txtLockModel"
            app:layout_constraintTop_toTopOf="@+id/txtLockModel" />

      <EditText
        android:id="@+id/txtLockInternalId"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/internal_id"
        style="@style/mirrorText"
        android:includeFontPadding="false"
        android:padding="14dp"
        android:singleLine="true"
        android:textAllCaps="true"
        android:textColor="@color/black"
        android:visibility="gone"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtLockModel" />

      <View
        android:id="@+id/viewInternalIdScanning"
        android:layout_width="70dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/txtLockInternalId"
        app:layout_constraintEnd_toEndOf="@+id/txtLockInternalId"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@+id/guideline3"
        app:layout_constraintTop_toTopOf="@+id/txtLockInternalId" />


      <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:visibility="gone"
        android:src="@drawable/iv_scan_add_new"
        app:layout_constraintBottom_toBottomOf="@+id/txtLockInternalId"
        app:layout_constraintEnd_toEndOf="@+id/txtLockInternalId"
        app:layout_constraintTop_toTopOf="@+id/txtLockInternalId" />

        <TextView
            android:id="@+id/tv_building_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/time_zone"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtLockInternalId" />

        <EditText
            android:id="@+id/selectTimeZone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:clickable="true"
            android:drawableEnd="@drawable/ic_arrow_downward"
            android:editable="false"
            android:focusable="false"
            style="@style/mirrorText"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/select_time_zone"
            android:includeFontPadding="false"
            android:minWidth="400dp"
            android:padding="14dp"
            android:singleLine="true"
            android:text="UTC+04"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_building_name" />


        <TextView
            android:id="@+id/tv_floor_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/sizes"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/selectTimeZone" />

        <EditText
            android:id="@+id/selectSize"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_edit_corners"
            android:clickable="true"
            android:drawableEnd="@drawable/ic_arrow_downward"
            android:editable="false"
            android:focusable="false"
            style="@style/mirrorText"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/select_size"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toStartOf="@+id/guideline3"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_floor_number" />

        <TextView
            android:id="@+id/tv_apartment_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/color"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="@+id/guideline3"
            app:layout_constraintTop_toTopOf="@+id/tv_floor_number" />

        <EditText
            android:id="@+id/selectColor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:clickable="true"
            android:drawableEnd="@drawable/ic_arrow_downward"
            android:editable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_medium_500"
            style="@style/mirrorText"
            android:hint="@string/select_color"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="@+id/et_brand"
            app:layout_constraintStart_toStartOf="@+id/tv_apartment_number"
            app:layout_constraintTop_toBottomOf="@+id/tv_apartment_number" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />


        <TextView
            android:id="@+id/btnSave"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="43dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="41dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/btnSave"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressLay"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>