package data.network.android

import android.annotation.SuppressLint
import android.util.Log
import com.google.gson.JsonObject
import data.network.android.models.CreateRoutineRequest
import data.network.android.models.LockHistoryModel
import data.network.android.models.LogsSendModel
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.RevokeResponse
import keyless.data.network.android.ConfigValues
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import java.io.File

@SuppressLint("Check`Result")
object ApiUtils {

    val BASE_URL = ConfigValues.baseUrl // dev
    val IMAGE_BASE_URL = "${ConfigValues.baseImageUrl}/" // dev
    val Web_FORGOT_PASSWORD = "${ConfigValues.forgotPasswordUrl}/" // dev

//    const val BASE_URL = "https://api.keyless.ae" //live
//    const val IMAGE_BASE_URL = "https://api.keyless.ae/" //live
//    const val Web_FORGOT_PASSWORD = "https://login.keyless.ae/forgot-password/" // live

//    const val BASE_URL = "https://api.phase1-staging.keyless-testing.com" // staging
//    const val IMAGE_BASE_URL = "https://api.phase1-staging.keyless-testing.com/" // staging
//    const val Web_FORGOT_PASSWORD = "https://login.phase1-staging.keyless-testing.com/forgot-password/" // staging

//    const val BASE_URL = "https://api.phase1-production.keyless.ae" // production new not in use
//    const val IMAGE_BASE_URL = "https://api.phase1-production.keyless.ae/" // production new
//    const val Web_FORGOT_PASSWORD = "https://login.phase1-production.keyless.ae/forgot-password/" // production new

    val servicesInterface = ApiServicesInterface.create(BASE_URL)


    suspend fun getUserProfile(token: String, p1: (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getUserProfile(
                token = token
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun editUserProfile(
        token: String,
        photo: MultipartBody.Part?,
        first_name: RequestBody,
        last_name: RequestBody,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.editUserProfile(
                token = token,
                photo = photo,
                first_name = first_name,
                last_name = last_name
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCompanyProfileApi(token: String, p1: (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getCompanyProfile(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

//    suspend fun getDeleteAccountApi(
//        token: String,
//        id: String,
//        p1: suspend (Any) -> Unit,
//        p2: (Exception) -> Unit
//    ) {
//        try {
//            val response = servicesInterface.deleteAccountApi(token, id)
//            withContext(Dispatchers.Main) { p1(response) }
//        } catch (e: Exception) {
//            withContext(Dispatchers.Main) { p2(e) }
//        }
//    }

    suspend fun upateCompanyProfile(
        token: String,
        data: JsonObject,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.updateCompanyProfile(token, data)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getAllProperty(token: String, p1: (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getAllProperty(
                token = token
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    private fun getRequestBody(value: String?): RequestBody? {
        if (value == null) {
            return null
        }

        val contentType = "text/plain".toMediaTypeOrNull()
        return value.toRequestBody(contentType)
    }

    private fun getRequestBodyNonNull(value: String): RequestBody =
        value.toRequestBody("text/plain".toMediaTypeOrNull())

    suspend fun getCreateProperty(
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        laundary_number: String,
        grocery_number: String,
        icon_id: String,
        call: String,
        whatsapp: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.propertyCreate(
                token = token,
                latitude = latitude,
                longitude = longitude,
                emirate = emirate,
                building_name = building_name,
                total_floors = total_floors,
                area = area,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id, call, whatsapp
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun updateProperty(
        properyID: String,
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        laundary_number: String,
        grocery_number: String,
        icon_id: String,
        support: String,
        whatsapp: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.updateProperty(
                token = token,
                properyID,
                latitude = latitude,
                longitude = longitude,
                emirate = emirate,
                building_name = building_name,
                total_floors = total_floors,
                area = area,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id,
                support, whatsapp
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun postAddUserNameApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.addUserApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun postSendOtpMobileApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.sendOtpMobileApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
//    suspend fun postVerifyMobileOtpApi(
//        request: VerifyMobileOtpApiRequest,
//        p1: (Any) -> Unit,
//        p2: (Exception) -> Unit
//    ) {
//        try {
//            val response = servicesInterface.verifyMobileOtpApiRequest(request)
//            withContext(Dispatchers.Main) { p1(response) }
//        } catch (e: Exception) {
//            withContext(Dispatchers.Main) { p2(e) }
//        }
//    }
//
//
    suspend fun postSendOtpEmailApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.sendOtpEmailApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
//
    suspend fun postVerifyEmailOtpApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.verifyEmailOtpApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
//
    suspend fun postResendOtpApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.resendOtpApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
    suspend fun postWithEmailLoginApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.loginWithEmailApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
    suspend fun postLoginApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.loginApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    //
    suspend fun postVerifyLoginOtpApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.verifyLoginOtpApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun forgotPassword(
        req: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.forgotPassword(req)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun postVerifyForgotOtpApi(
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.verifyForgotOtpApiRequest(request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLogoutAccount(
        token: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLogoutAccount(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun changePassword(
        token: String,
        req: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.changePassword(token, req)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun changePasswordForgot(
        token: String,
        req: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.changePasswordForgot(token, req)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun emailHitUpdate(
        token: String,
        req: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitEmailApiUpdate(token, req)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun verifyEmailOtp(
        token: String,
        req: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.verifyEmailOtp(token, req)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitGetKeyByInternalId(
        token: String,
        code: String,
        arguments: String,
        uuid: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            if (arguments == "installer") {
                val response = servicesInterface.getKeyByInterIdAdmin(token, code, uuid)
                withContext(Dispatchers.Main) { p1(response) }
            } else {
                val response = servicesInterface.getKeyByInterId(token, code)
                withContext(Dispatchers.Main) { p1(response) }
            }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitGetKeyByCode(
        token: String,
        code: String,
        arguments: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLockIdByScanKey(token = token, code = code)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCompanyProperty(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.companyPropertyAll(
                token = token
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getAllIcons(token: String, p1: suspend (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getAllIcons(
                token = token
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitAssignLockApi(
        token: String,
        data: JsonObject,
        whichPlace: String,
        companyId: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            if (whichPlace == "installer") {
                val response = servicesInterface.hitAssignLockApiInstaller(token, data, companyId)
                withContext(Dispatchers.Main) { p1(response) }
            } else {
                val response = servicesInterface.hitAssignLockApi(token, data)
                withContext(Dispatchers.Main) { p1(response) }
            }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitUpdateInstallationStatus(
        token: String,
        data: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: suspend (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitUpdateInstallationStatus(token, data)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun privacyModeApi(
        token: String,
        id: String,
        status: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.privacyLockMode(token, id, status)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun updateLockLogs(
        token: String,
        obj: ArrayList<LockHistoryModel>,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val model = LogsSendModel()
            model.data = obj
            val response = servicesInterface.lockLogs(token, model)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitLogApi(
        token: String,
        id: String,
        deviceName: String,
        uuid: String,
        lock_id: String,
        status: String,
        validFrom: String,
        validTo: String,
        assignment_id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response =
                servicesInterface.shareAccessLogs(
                    token,
                    id,
                    lock_id,
                    status,
                    deviceName,
                    uuid,
                    validFrom,
                    validTo,
                    assignment_id
                )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun postBatteryPercentage(
        token: String,
        batteryCount: Int,
        uniqueKey: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.postBatteryPercentage(token, batteryCount, uniqueKey)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLockAccessLog(
        token: String,
        id: String,
        page: JsonObject,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLockLog(token, id, page)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLockSharedLog(
        token: String,
        id: String,
        page: JsonObject,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLockSharedLog(token, id, page)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getEditLockApi(
        token: String,
        id: String,
        buildId: String,
        floorNumber: String,
        appartmentNumber: String,
        lockName: String,
        iconId: String,
        mapId: String,
        unitId: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.editLock(
                token,
                id,
                buildId,
                appartmentNumber,
                appartmentNumber,
                lockName,
                floorNumber,
                iconId,
                mapId, 1, unitId
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getUserList(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getUserList(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLockInfoDetail(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLockInfoDetail(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun userRevokeApi(
        token: String,
        requset: RevokeResponse,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.userRevoke(token, requset)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getUserSearchList(
        token: String,
        id: String,
        searchText: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getUserSearchList(token, id, searchText)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLocksDetails(
        token: String,
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLocksDetails(token, request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getLockInvite(
        token: String,
        request: JsonObject,
        email: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLocksInvite(token, email, request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCancelInvite(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getCancelInvite(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun adminLocksChinese(
        token: String,
        page: Int,
        search: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.adminLocksChinese(token, page, 10, search)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun adminDeleteLocksChinese(
        token: String,
        id: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.adminDeleteLocksChinese(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getSizeColorData(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.adminCommonDetails(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun adminCreateLocksChinese(
        token: String,
        request: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.adminCreateLocksChinese(token, request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getRoutine(
        token: String,
        page: Int,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getRoutine(token, page)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun addRoutine(
        token: String,
        request: CreateRoutineRequest,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.addRoutine(token, request)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun editRoutine(
        token: String,
        id: String,
        request: CreateRoutineRequest,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.editRoutine(
                token,
                id,
                request

            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getSingleRoutine(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getSingleRoutine(
                token,
                id

            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun deleteTimeRange(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.deleteTimeRangeRoutine(
                token = token,
                id = id
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun deleteRoutine(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.deleteRoutine(
                token,
                id

            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getSysCodeHistory(
        bleName: JsonObject,
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getSysCodeHistory(
                token,
                bleName
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun setSysCodeHistorySubmit(
        requestBody: JsonObject,
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.setSysCodeHistorySubmit(
                token,
                requestBody
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitEditAccess(
        token: String,
        jsonObject: JsonObject,
        _id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitEditAccess(
                token,
                jsonObject,
                _id
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCardSeries(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getCardSeries(
                token
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun createCard(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.createCard(
                token,
                jsonObject
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getConfiguredCardList(
        token: String,
        page: Int,
        search: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getConfiguredCardList(
                token,
                page,
                search
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCardsList(
        token: String,
        search: String,
        page: Int,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getCardsList(
                token,
                search,
                page
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun configureCard(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.configureCard(
                token,
                jsonObject
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun deleteManageCard(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.deleteManageCard(
                token,
                jsonObject
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getInstallerList(
        token: String,
        page: Int,
        search: String,
        company_id: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getInstallerList(
                token,
                page,
                search,
                50,
                company_id
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getProperty(
        token: String,
        _id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getProperty(
                token,
                _id
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitCreateInstallerProperty(
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        laundary_number: String,
        grocery_number: String,
        icon_id: String,
        call: String,
        whatsapp: String,
        id: String,
        p1: (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitCreateInstallerProperty(
                token = token,
                latitude = latitude,
                longitude = longitude,
                emirate = emirate,
                building_name = building_name,
                total_floors = total_floors.toInt(),
                area = area,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id, call, whatsapp, id
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun updateStatus(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.updateStatus(
                token,
                jsonObject
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun assign_lock_iseo(
        token: String,
        userRole: String,
        list: ModelAdminInstaller.DataModelInstaller,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            var response = if (userRole == "Installer") {
                servicesInterface.admin_assign_lock_iseo(
                    token,
                    list.company[0]._id,
                    jsonObject
                )
            } else {
                servicesInterface.assign_lock_iseo(
                    token,
                    jsonObject
                )
            }
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun updateMaintenanceStatus(
        token: String,
        lockId: JsonObject,
        userRole: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            if (userRole == "Installer") {
                val response = servicesInterface.updateMaintenanceStatus(token, lockId)
                withContext(Dispatchers.Main) { p1(response) }
            } else {
                val response = servicesInterface.updateMaintenanceStatusUser(token, lockId)
                withContext(Dispatchers.Main) { p1(response) }
            }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getMaintenanceList(
        token: String,
        page: Int,
        search: String,
        uuid: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getMaintenanceList(
                token,
                page,
                search,
                50,
                uuid
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun platformDetails(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getPlatformDetails(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun upgradeMaintenance(
        token: String,
        lockId: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.upgradeMaintenance(token, lockId)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun downgradeMaintenance(
        token: String,
        lockId: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.downgradeMaintenance(token, lockId)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun assign_lock_iseo_installer(
        token: String,
        _id: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            var response = servicesInterface.admin_assign_lock_iseo(token, _id, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun sentErrorLog(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            var response = servicesInterface.sentErrorLog(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getCheckUser(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getCheckUser(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
//            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitWhtsappOtp(
        requestBody: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitWhtsappOtp(requestBody)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
//            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitPhoneOtp(
        requestBody: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.hitPhoneOtp(requestBody)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
//            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitGetAllUsers(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getAllUsers(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun hitLoginAsUser(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getLoginAsUser(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun claimKey(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.claimKey(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun forceUpdateApi(
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.forceUpdateApi()
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getServicesApi(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getServicesApi(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getUserLias(token: String, p1: suspend (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getUserLias(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getFirmware(token: String, p1: suspend (Any) -> Unit, p2: (Exception) -> Unit) {
        try {
            val response = servicesInterface.getFirmware(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun updateFirmwareStatus(
        token: String,
        jsonObject: JsonObject,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.updateFirmwareStatus(token, jsonObject)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getGuestAssignments(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getGuestAssignments(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun getAllUnits(
        token: String,
        id: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getAllUnits(token, id)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun checkInComplete(
        name: String,
        bookingNumber: String,
        documentNumber: String?,
        status: String,
        documentType: String,
        identity1: String?,
        identity2: String?,
        documentImageApi: String?,
        myImageApi: String?,
        similarity: String?,
        token: String?,
        receivedJsonArray: JSONArray,
        expiry_date: String?,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.checkInComplete(
                token!!,
                getRequestBodyForField(name),
                getRequestBodyForField(bookingNumber),
                getRequestBodyForField(documentNumber),
                getRequestBodyForField(status),
                getRequestBodyForField(documentType),
                getRequestBodyForField(similarity),
                getRequestBodyForField(expiry_date),
                getMultipartBody(identity1.toString(), "identity1"),
                getMultipartBody(identity2.toString(), "identity2"),
                getMultipartBody(documentImageApi.toString(), "document_image"),
                getMultipartBody(myImageApi.toString(), "captured_image"),
                receivedJsonArray
            )
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    private fun getRequestBodyForField(field: String?): RequestBody? {
        return field?.toRequestBody("text/plain".toMediaTypeOrNull())
    }

    private fun getMultipartBody(filePath: String?, fileName: String): MultipartBody.Part? {
        var multiPartBody: MultipartBody.Part? = null
        if (filePath?.isNotEmpty() == true) {
            val file = File(filePath)
            if (file.exists()) {
                val requestBodyProfileImage = file.asRequestBody("image/*".toMediaTypeOrNull())
                multiPartBody =
                    MultipartBody.Part.createFormData(fileName, file.name, requestBodyProfileImage)
            }
        }
        if (multiPartBody == null) {
            Log.e("TAG", "Multipart Body is NULL !!!!!")
        }
        return multiPartBody
    }

    suspend fun getNotificationData(
        token: String,
        page: Int,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.getNotificationData(token, page)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }

    suspend fun readNotifications(
        token: String,
        p1: suspend (Any) -> Unit,
        p2: (Exception) -> Unit
    ) {
        try {
            val response = servicesInterface.readNotifications(token)
            withContext(Dispatchers.Main) { p1(response) }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) { p2(e) }
        }
    }
}