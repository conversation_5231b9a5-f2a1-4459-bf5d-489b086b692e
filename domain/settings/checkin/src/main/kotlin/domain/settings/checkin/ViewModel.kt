package domain.settings.checkin

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.user.guest.repositories.GuestRepository
import domain.settings.checkin.models.Event
import domain.settings.checkin.models.ScreenEvent
import domain.settings.checkin.repositories.ScreenStateRepository
import domain.settings.checkin.usecases.SettingsCheckInUseCases

class ViewModel(
    repository: GuestRepository,
    logger: Logger,
    status: StatusRepository
) {

    private val screen = ScreenStateRepository()
    private val useCases = SettingsCheckInUseCases(
        screen = screen,
        repository = repository,
        logger = logger,
        status = status
    )
    val screenStream = screen.stream

    suspend fun onEvent(event: Event) = when (event) {
        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.Init -> useCases.init.execute(event)
    }
}