package feature.routines

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.RoutineModel
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.routines.databinding.FragmentRoutinesBinding


class RoutinesFragment : Fragment(), RoutinesAdapter.ClickToEdit {

    private var stopPagination: Boolean = false
    private val listTotal: ArrayList<RoutineModel> = ArrayList()
    private var param: String = ""
    lateinit var mViewModel: RoutineViewModel
    private lateinit var adapterRoutines: RoutinesAdapter
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    var layoutManager = LinearLayoutManager(context)
    var page = 1
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private lateinit var binding: FragmentRoutinesBinding


    val startActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
        ActivityResultCallback() {
            if (it.resultCode == Activity.RESULT_OK) {
                apiImplementation(1, "no")
            }
            // Add same code that you want to add in onActivityResult method
        })


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentRoutinesBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        setRecyclerView()
        clickListeners()
        pagination()
        apiImplementation(page, "no")
        observerInit()

    }

    private fun pagination() {
        if (!stopPagination) {
            binding.rvRoutines.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
                override fun isLastPage(): Boolean {
                    return isLastPage
                }

                override fun loadMoreItems() {
                    if (!isLoadingMore) {
                        page++
                        apiImplementation(page, "yes")
                        binding.progressPagination.visibility = View.VISIBLE
                    }
                }

                override fun isLoading(): Boolean {
                    return isLoadingMore
                }

            })
        }


    }

    private fun clickListeners() {
        binding.ivAddRoutines.setOnClickListener {
            if (binding.noInternetLayout.isVisible) {
                requireContext().toast(getString(keyless.feature.common.R.string.you_are_in_offline))
            } else {
                startActivityIntent.launch(Intent(requireActivity(), AddRoutineActivity::class.java))
//                startActivityForResult(
//                    Intent(requireActivity(), AddRoutineActivity::class.java),
//                    14
//                )
            }
        }

        binding.ivAddRoutine.setOnClickListener {
            if (binding.noInternetLayout.isVisible) {
                requireContext().toast(getString(keyless.feature.common.R.string.you_are_in_offline))
            } else {
                startActivityIntent.launch(Intent(requireActivity(), AddRoutineActivity::class.java))
//                startActivityForResult(
//                    Intent(requireActivity(), AddRoutineActivity::class.java),
//                    14
//                )
            }
        }

        binding.viewBack.setOnClickListener {
            requireActivity().finish()
        }

    }

//    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
//        if (resultCode == Activity.RESULT_OK) {
//            apiImplementation(1, "no")
//        }
//    }

    private fun observerInit() {
//        mViewModel.progress.observe(viewLifecycleOwner) {
//            if (it) {
//                ProgressDialogUtils.getInstance().hideProgress()
//                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
//            } else {
//                ProgressDialogUtils.getInstance().hideProgress()
//            }
//        }

        mViewModel.error.observe(viewLifecycleOwner) {
            if (it == getString(keyless.feature.common.R.string.you_are_in_offline)) {
                binding.noInternetLayout?.visibility = View.VISIBLE
            } else {
                requireContext().toast(it)
            }
            binding.progressLay.visibility = View.GONE
        }
    }

    private fun setRecyclerView() {
        param = arguments?.getString("whichScreen").toString()
        if (param == "onlyList") {
            binding.titlePage.isVisible = false
            binding.ivAddRoutines.isVisible = false
            binding.constraintShare.isVisible = true
        } else {
            binding.titlePage.isVisible = true
            binding.ivAddRoutines.isVisible =
                !(Preferences.role.get() == Roles.CUSTOMER_SERVICES || Preferences.role.get() == Roles.VIEWER_ACCESS)
            binding.constraintShare.isVisible = false
        }
        binding.rvRoutines.layoutManager = layoutManager
        adapterRoutines = RoutinesAdapter(this, param)
        binding.rvRoutines.adapter = adapterRoutines
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[RoutineViewModel::class.java]

    }

    private fun apiImplementation(page: Int, isPagination: String) {
        binding.progressLay.isVisible = isPagination != "yes"
        mViewModel.hitGetRoutineApi(sharePrefs.token, page, isPagination)
            .observe(viewLifecycleOwner) {
                if (it.success) {
                    binding.noDataView.isVisible = false
                    binding.noInternetLayout?.visibility = View.GONE
                    if (page == 1) {
                        listTotal.clear()
                    }
                    listTotal.addAll(it.routines)
                    adapterRoutines.updateRoutines(listTotal)
                    if (listTotal.size == it.total_count) {
                        isLastPage = true
                        isLoadingMore = true
                        stopPagination = true
                    }
                    binding.progressPagination.visibility = View.GONE
                } else {
                    if (page == 1) {
                        if (it.routines.size == 0) {
                            listTotal.clear()
                            adapterRoutines.updateRoutines(listTotal)
                        }
                    }
                }
                binding.noDataView.isVisible = listTotal.size <= 0
                binding.progressLay.visibility = View.GONE
            }
    }

    override fun editRoutine(model: RoutineModel, param: String) {
        if (param == "onlyList") {
            val intent = Intent(requireActivity(), SelectRoutineActivity::class.java)
            intent.putExtra("routine", model)
            requireActivity().setResult(Activity.RESULT_OK, intent)
            requireActivity().finish()
        } else {
            startActivityIntent.launch(
                Intent(requireActivity(), AddRoutineActivity::class.java).putExtra(
                    "model",
                    model
                )
            )
//            startActivityForResult(
//                Intent(
//                    requireActivity(),
//                    AddRoutineActivity::class.java
//                ).putExtra("model", model), 14
//            )
        }
    }
}