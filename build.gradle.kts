plugins {
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.kapt) apply false
    alias(libs.plugins.kotlin.serialization) apply false
    alias(libs.plugins.ktlint.gradle) apply false
}

allprojects {
    apply(plugin = rootProject.libs.plugins.ktlint.gradle.get().pluginId)

    configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
        filter {
            exclude("*.gradle.kts")
            exclude {
                it.file.path.contains("${buildDir}/generated/")
            }
        }
    }
}

task("build") {
    dependsOn("v364CoreSdk:build")
    dependsOn("v364DroidSdk:build")
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
}