plugins {
    id("keyless.kotlin")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-ktor"))
    implementation(project(":core-monitoring-test"))
    implementation(project(":core-lock-common"))
    implementation(project(":core-lock-logs"))
    implementation(project(":core-lock-airbnk"))
    implementation(project(":core-lock-iseo"))
    implementation(project(":core-locks-manager"))
    implementation(project(":core-lock-rayonics"))
    implementation(project(":core-lock-ttlock"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":data-common"))
    implementation(project(":data-user-home"))
}
