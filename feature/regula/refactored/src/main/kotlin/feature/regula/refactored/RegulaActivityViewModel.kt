package feature.regula.refactored

import androidx.lifecycle.ViewModel
import domain.common.ErrorHandler
import kotlin.coroutines.CoroutineContext

class RegulaActivityViewModel(
    private val viewModel: domain.regula.ViewModel,
    private val handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    coroutineContext: CoroutineContext? = null,
    private val onClear: () -> Unit
) : ViewModel() {

    fun onDestroy() {
        viewModel.deInitRegula()
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}