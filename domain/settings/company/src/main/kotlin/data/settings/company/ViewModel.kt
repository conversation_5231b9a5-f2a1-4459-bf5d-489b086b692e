package data.settings.company

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.company.repositories.CompanyRepository
import data.settings.company.models.CompanyEvent
import data.settings.company.models.Event
import data.settings.company.models.ScreenEvent
import data.settings.company.repositories.ScreenStateRepository
import data.settings.company.repositories.SideEffectsRepository
import data.settings.company.usecases.CompanyProfileUseCases

class ViewModel(
    company: CompanyRepository,
    logger: Logger,
    status: StatusRepository
) {
    private val screenState = ScreenStateRepository()
    val sideEffects = SideEffectsRepository()

    private val useCases = CompanyProfileUseCases(company, screenState, sideEffects, logger, status)

    val screenStream = screenState.stream

    suspend fun onEvent(event: Event) = when (event) {
        is ScreenEvent -> onScreenEvent(event)

        is CompanyEvent -> onCompanyEvent(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.Init -> useCases.initScreen.execute(event)
    }

    private suspend fun onCompanyEvent(event: CompanyEvent) = when (event) {
        is CompanyEvent.UpdateCompany -> useCases.updateCompanyProfile.execute(event)
    }
}