<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.authentication.login.LoginUserDetailsFragment">


    <TextView
        android:id="@+id/textViewHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/sign_in_to_your_account"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewUserName"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />

    <View
        android:id="@+id/viewMobileNumber"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline2"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />

    <ImageView
        android:id="@+id/imgMobileNumber"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_grey_6_round"
        android:padding="6dp"
        android:visibility="gone"
        android:src="@drawable/iv_mobile_grey"
        app:layout_constraintStart_toStartOf="@+id/guideline2"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/mobile_number"
        android:textColor="@color/black"
        android:visibility="gone"
        android:fontFamily="@font/poppins_regular_400"
        app:layout_constraintBottom_toBottomOf="@+id/imgMobileNumber"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="6dp"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/imgMobileNumber"
        app:layout_constraintTop_toTopOf="@+id/imgMobileNumber" />


    <ImageView
        android:id="@+id/imgUserName"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_blue_main"
        android:padding="6dp"
        android:visibility="gone"
        android:src="@drawable/iv_username"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/username_email"
        android:layout_marginStart="6dp"
        android:visibility="gone"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@+id/imgUserName"
        app:layout_constraintStart_toEndOf="@+id/imgUserName"
        app:layout_constraintTop_toTopOf="@+id/imgUserName" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:id="@+id/constraintUserName"
        android:layout_height="0dp"
        android:layout_marginTop="34dp"
        app:layout_constraintBottom_toTopOf="@+id/btnSignUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imgUserName"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/username_email"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <EditText
            android:id="@+id/tvUserName"
            style="@style/mirrorText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/username_email"
            android:padding="14dp"
            android:inputType="text"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/password"
            android:textSize="16dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserName" />

        <EditText
            android:id="@+id/tvPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_edit_corners"
            android:hint="@string/password"
            android:layout_marginTop="4dp"
            android:inputType="textPassword"
            android:textSize="16dp"
            android:fontFamily="@font/poppins_medium_500"
            android:padding="14dp"
            style="@style/mirrorText"
            android:textAlignment="viewStart"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView3" />

        <ImageView
            android:id="@+id/showPassBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/tvPassword"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvPassword" />



        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/forgot_password_q"
            android:id="@+id/forgotPassword"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPassword" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="34dp"
        android:id="@+id/constraintMobile"
        app:layout_constraintBottom_toTopOf="@+id/btnSignUp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imgUserName"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/textView11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/mobile_number"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/singupCountry"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@drawable/bg_edit_corners"
            android:elevation="1dp"
            android:orientation="horizontal"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="12dp"
            android:paddingBottom="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvMobileNumber"
            app:layout_constraintStart_toStartOf="@+id/textView11"
            app:layout_constraintTop_toTopOf="@+id/tvMobileNumber">


            <com.mikhaellopez.circularimageview.CircularImageView
                android:id="@+id/iv_flag_img"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                app:civ_border="false"
                tools:src="@drawable/flag_uae" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:src="@drawable/arrow_black_down" />

        </LinearLayout>


        <EditText
            android:layout_marginStart="20dp"
            android:id="@+id/tvMobileNumber"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/mobile_number"
            android:inputType="number"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            style="@style/mirrorText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/singupCountry"
            app:layout_constraintTop_toBottomOf="@+id/textView11" />


    </androidx.constraintlayout.widget.ConstraintLayout>





    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        app:layout_constraintGuide_percent="0.5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/btnSignUp"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/login"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>