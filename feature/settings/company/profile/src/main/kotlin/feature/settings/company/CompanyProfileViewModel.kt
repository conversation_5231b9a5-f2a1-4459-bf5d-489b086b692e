package feature.settings.company

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import data.settings.company.models.Event
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class CompanyProfileViewModel(
    private val viewModel: data.settings.company.ViewModel,
    private val handler: <PERSON>rror<PERSON><PERSON><PERSON>,
    coroutineContext: CoroutineContext? = null,
    private val onClear: () -> Unit
) : ViewModel() {

    private val scope = if (coroutineContext != null) viewModelScope + coroutineContext else viewModelScope

    val screenStream = viewModel.screenStream
    val sideEffects = viewModel.sideEffects.stream

    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}