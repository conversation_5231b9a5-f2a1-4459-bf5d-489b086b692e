package feature.regula.refactored.documents.models

import core.common.status.Status
import core.regula.documents.DocumentScanResult
import domain.regula.models.ScreenState

sealed interface UIScreenState {
    val status: List<Status>
}

internal data class UILoadingState(
    override val status: List<Status>
) : UIScreenState


internal data class UIReadyState(
    val documentTypes: List<ScreenState.DocumentTypeState>,
    val selectedDocumentType: ScreenState.DocumentTypeState?,
    val frontSide: DocumentScanResult?,
    val backSide: DocumentScanResult?,
    override val status: List<Status>
) : UIScreenState {
    val isDocumentScanComplete =
        (frontSide?.firstName != null || backSide?.firstName != null) &&
                (frontSide?.lastName != null || backSide?.lastName != null) &&
                (frontSide?.dateOfBirth != null || backSide?.dateOfBirth != null) &&
                (frontSide?.expiryDate != null || backSide?.expiryDate != null) &&
                (frontSide?.image != null || backSide?.image != null) &&
                (frontSide?.portrait != null || backSide?.portrait != null) &&
                (frontSide?.documentNumber != null || backSide?.documentNumber != null)
}