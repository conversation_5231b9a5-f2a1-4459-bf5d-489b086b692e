package data.utils.android.database

import android.content.Context
import android.util.Log
import androidx.room.Room
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

object Constant {

    var db: AppDataBase? = null

    fun getDataBase(applicationContext: Context): AppDataBase? {
        if (db == null) {
            val MIGRATION = object : Migration(10, 11) {
                override fun migrate(database: SupportSQLiteDatabase) {
                    Log.e("//", "migrate:10-11 ")
                    database.execSQL(
                        "CREATE TABLE IF NOT EXISTS `Answers` (" +
                            "`id` INTEGER, `question_id` INTEGER,`custom_prompt` STRING," +
                            "`questionStatus` BOOLEAN, `answer` STRING,  `date` STRING, " +
                            "PRIMARY KEY(`id`))"
                    )
                    database.execSQL(
                        "CREATE TABLE IF NOT EXISTS `Question` (`id` INTEGER, `quote` STRING, `question` STRING, " +
                            "`statusQuestion` BOOLEAN, `hint` STRING, `prompt` STRING, " +
                            "PRIMARY KEY(`id`))"
                    )
                    database.execSQL(
                        "CREATE TABLE IF NOT EXISTS `QuotesModel` (`id` INTEGER, `quote` STRING, " +
                            "`authorname` STRING, " + "PRIMARY KEY(`id`))"
                    )
                    database.execSQL(
                        "CREATE TABLE IF NOT EXISTS `ReminderQuestion` (`id` INTEGER, `mtime` LONG, " +
                            "PRIMARY KEY(`id`))"
                    )
//                    database.execSQL("ALTER TABLE Answers RENAME COLUMN question TO custom_prompt")
                }
            }
            val MIGRATION2 = object : Migration(11, 12) {
                override fun migrate(database: SupportSQLiteDatabase) {
                    Log.e("//", "migrate:11-12 ")
                }
            }
            return Room.databaseBuilder(
                applicationContext,
                AppDataBase::class.java,
                "LockLogs"
            ).allowMainThreadQueries().fallbackToDestructiveMigration().build()
        } else {
            return db
        }
    }
}