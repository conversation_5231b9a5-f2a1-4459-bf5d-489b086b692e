concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Check development

on:
  push:
    branches:
      - hotfix/*
      - fix/*
      - feature/*
      - refactor/*
      - update/*
  workflow_dispatch:

jobs:
  check-linux:
    name: Check linux development
    uses: ./.github/workflows/reusable-check-development.yml
    secrets: inherit
    with:
      environment: "dev"
      machine: "ubuntu-22.04"

#  check-mac:
#    name: Check mac development
#    uses: ./.github/workflows/reusable-check-development.yml
#    secrets: inherit
#    needs: check-linux
#    with:
#      environment: "dev"
#      machine: "macos-latest"

  remove-deployments:
    runs-on: ubuntu-22.04
    needs: [check-linux]
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/deployments-delete