package feature.regula.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class TransactionInfo(

    @SerializedName("ComputerName") var ComputerName: String? = null,
    @SerializedName("DateTime") var DateTime: String? = null,
    @SerializedName("SystemInfo") var SystemInfo: String? = null,
    @SerializedName("TransactionID") var TransactionID: String? = null,
    @SerializedName("UserName") var UserName: String? = null,
    @SerializedName("Version") var Version: String? = null

)