package data.user.account.repositories

import core.http.client.HttpResponse

interface UserAccountRemote {

    suspend fun sendEmailOtp(email: String, authentication: String): HttpResponse

    suspend fun verifyEmail(
        email: String,
        otp: String,
        method: String = "email",
        deviceType: String,
        userId: String,
        authentication: String
    ): HttpResponse

    suspend fun changePassword(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userId: String,
        isAdmin: Boolean,
        authentication: String
    ): HttpResponse

    suspend fun updateProfile(
        firstName: String,
        lastName: String,
        authentication: String
    ): HttpResponse

    suspend fun checkIn(
        name: String,
        bookingNumber: String,
        documentNumber: String,
        status: String,
        documentType: String,
        similarity: String,
        expiryDate: String,
        identity1: ByteArray,
        identity2: ByteArray?,
        documentImage: ByteArray,
        capturedImage: ByteArray,
        extra: String,
        authentication: String
    ): HttpResponse

    suspend fun getUserProfile(authentication: String): HttpResponse

    suspend fun deleteAccount(authentication: String): HttpResponse
}