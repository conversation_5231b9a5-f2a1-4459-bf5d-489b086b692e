package core.lock.nordicfirmware

import android.app.Activity
import no.nordicsemi.android.dfu.DfuBaseService

internal class DeviceFirmUpdateService : DfuBaseService() {
    override fun getNotificationTarget(): Class<out Activity> {
        return Class.forName(NordicFirmwareRepository.installActivityQualifier) as Class<out Activity>
    }

    override fun onCreate() {
        super.onCreate()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun isDebug(): Boolean {
        return true
    }
}