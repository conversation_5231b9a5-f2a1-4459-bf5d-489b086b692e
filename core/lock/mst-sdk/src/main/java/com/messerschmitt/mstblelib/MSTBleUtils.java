package com.messerschmitt.mstblelib;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;


/**
 * Method Class that includes helper functions for Core Bluetooth functionality
 *
 * <AUTHOR> Systems GmbH
 * @version 2020.1001
 */
public class MSTBleUtils {
    private final static int REQUEST_ENABLE_BT = 2001;
    private static int RSSI_Batt;
    private static int RSSI_12V;
    private final Activity mActivity;

    private final BluetoothAdapter mBluetoothAdapter;

    public MSTBleUtils(final Activity activity, final int _RSSI_Batt, final int _RSSI_12V) {
        mActivity = activity;
        RSSI_Batt = _RSSI_Batt;
        RSSI_12V =  _RSSI_12V;

      /*  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.requestPermissions(
                    mActivity,
                    new String[]
                            {
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.ACCESS_COARSE_LOCATION,
                                    Manifest.permission.BLUETOOTH_SCAN,
                                    Manifest.permission.BLUETOOTH_CONNECT
                            }, 0);
        }
        else{
            ActivityCompat.requestPermissions(
                    mActivity,
                    new String[]
                            {
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.ACCESS_COARSE_LOCATION,
                                    Manifest.permission.BLUETOOTH,
                                    Manifest.permission.READ_EXTERNAL_STORAGE
                            }, 0);
        }*/

        final BluetoothManager btManager = (BluetoothManager) mActivity.getSystemService(Context.BLUETOOTH_SERVICE);
        mBluetoothAdapter = btManager.getAdapter();
    }

    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    public void askUserToEnableBluetoothIfNeeded() {
        if (isBluetoothLeSupported() && (mBluetoothAdapter == null || !mBluetoothAdapter.isEnabled())) {
            final Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            mActivity.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
        }
    }

    public BluetoothAdapter getBluetoothAdapter() {
        return mBluetoothAdapter;
    }

    public boolean isBluetoothLeSupported() {
        return mActivity.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE);
    }

    public boolean isBluetoothOn() {
        return mBluetoothAdapter != null && mBluetoothAdapter.isEnabled();
    }

    public int getRSSI_Batt(){
        return RSSI_Batt;
    }

    public int getRSSI_12V(){
        return RSSI_12V;
    }

    public void setRSSI_Batt(int val) {
        RSSI_Batt = val;
    }

    public void setRSSI_12V(int val) {
        RSSI_12V = val;
    }
}
