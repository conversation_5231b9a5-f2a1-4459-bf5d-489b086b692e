package com.app.keyless

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.app.keyless.databinding.ActivityTestingBinding

class TestingActivity : AppCompatActivity() {

    var path: String? = null
    lateinit var binding: ActivityTestingBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val uri = Uri.parse("android.resource://" + packageName + "/" + R.raw.new_vid)
        binding.surface.setVideoURI(uri)
        binding.surface.start()
//        surface.setVideoURI(getUriFromRawFile(this,R.raw.splash_vid))
    }

    private fun getUriFromRawFile(context: Context, rawResourceId: Int): Uri? {
        return Uri.Builder()
            .scheme(ContentResolver.SCHEME_ANDROID_RESOURCE)
            .authority(context.packageName)
            .path(rawResourceId.toString())
            .build()
    }
}