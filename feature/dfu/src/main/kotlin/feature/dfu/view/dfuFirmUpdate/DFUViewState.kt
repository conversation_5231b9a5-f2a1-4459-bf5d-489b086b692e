package feature.dfu.view.dfuFirmUpdate

import feature.dfu.data.DeviceFirmUpdateSettings

data class DFUViewState(
    val fileViewEntity: DFUSelectFileViewEntity = NotSelectedFileViewEntity(),
    val deviceViewEntity: DFUSelectDeviceViewEntity = DisabledSelectedDeviceViewEntity,
    val progressViewEntity: DFUProgressViewEntity = DisabledProgressViewEntity,
    val settings: DeviceFirmUpdateSettings? = null
) {
    fun isRunning(): Boolean {
        return (progressViewEntity as? WorkingProgressViewEntity)?.status?.isRunning() == true
    }

    fun isCompleted(): Boolean {
        return (progressViewEntity as? WorkingProgressViewEntity)?.status?.isCompleted() == true
    }
}