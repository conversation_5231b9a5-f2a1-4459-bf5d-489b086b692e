package data.utils.android.common

import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo

data class LockInfo(val lock: ILockScanInfo?, val info: IMobileCredentialScanInfo?) {
        override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
            return when (other) {
                is LockInfo -> {
                    return lock?.macAddress == other.lock?.macAddress
                }

                else -> super.equals(other)
            }
        }

        override fun hashCode(): Int {
            var result = lock?.hashCode() ?: 0
            result = 31 * result + (info?.hashCode() ?: 0)
            return result
        }
    }