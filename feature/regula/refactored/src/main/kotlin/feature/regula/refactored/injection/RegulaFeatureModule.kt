package feature.regula.refactored.injection

import domain.regula.injection.RegulaDomainScope
import feature.regula.refactored.RegulaActivityViewModel
import feature.regula.refactored.checkin.CheckInResultsAndroidViewModel
import feature.regula.refactored.documents.DocumentVerificationAndroidViewModel
import feature.regula.refactored.face.FaceVerificationAndroidViewModel
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module

val regulaFeatureModule = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<RegulaDomainScope>(RegulaDomainScope.getScopeId())

        DocumentVerificationAndroidViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = {  }
        )
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<RegulaDomainScope>(RegulaDomainScope.getScopeId())

        FaceVerificationAndroidViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = {  }
        )
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<RegulaDomainScope>(RegulaDomainScope.getScopeId())

        CheckInResultsAndroidViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = {  }
        )
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<RegulaDomainScope>(RegulaDomainScope.getScopeId())

        RegulaActivityViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = { scope.close() }
        )
    }
}