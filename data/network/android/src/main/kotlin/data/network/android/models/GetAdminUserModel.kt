package data.network.android.models

import androidx.annotation.Keep

@Keep
class GetAdminUserModel {
    var success: Boolean = false
    var message: String = ""
    var role: String = ""
    var user: UserModelAdmin = UserModelAdmin()
}

@Keep
class UserModelAdmin {

    var _id: String = ""
    var username: String = ""
    var user_type: String = ""
    var first_name: String = ""
    var role: String = ""
    var last_name: String = ""
    var passport_number: String = ""
    var profile_photo: String = ""
    var country_code: String = ""
    var email: String = ""
    var dob: String = ""
    var uid: String = ""
    var name: String = ""
    var device_model: String = ""
    var manager_id: String = ""
    var mobile_number: Long = 0
    var is_valid: Int = 0
    var integrator_type: String = ""
    var status: Int = 0
}