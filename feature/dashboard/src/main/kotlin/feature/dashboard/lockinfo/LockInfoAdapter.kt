package feature.dashboard.lockinfo

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.network.android.LocksListResponse
import keyless.feature.dashboard.databinding.LockInfoAdapterLayoutBinding

class LockInfoAdapter(var lockDetails: LocksListResponse.LocksModel?, val isGuest: Boolean) :
    RecyclerView.Adapter<LockInfoAdapter.ViewHolder>() {
    lateinit var context: Context
    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = LockInfoAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(lockDetails)
    }

    override fun getItemCount() = if (isGuest) 4 else 5

    inner class ViewHolder(val binding: LockInfoAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: LocksListResponse.LocksModel?) {
            if (isGuest) {
                when (position) {
                    0 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.building_name_dot
                        )
                        binding.txtValue.text = lockDetails!!.lock.property_details.name
                    }
                    1 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.floor_number_dot
                        )
                        binding.txtValue.text = lockDetails?.lock?.property_details?.floor
                    }
                    2 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.apartment_room_number_dot
                        )
                        binding.txtValue.text = lockDetails?.lock?.property_details?.appartment_number
                    }
                }
            } else {
                when (position) {
                    0 -> {
                        binding.txtHeading.text = context.getString(keyless.data.utils.android.R.string.brand1)
                        binding.txtValue.text = lockDetails!!.lock.provider
                    }
                    1 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.battery_level_dot
                        )
                        when (lockDetails!!.lock.battery_level) {
                            0 -> {
                                binding.txtValue.text = context.getString(keyless.data.utils.android.R.string.text_low)
                            }
                            1 -> {
                                binding.txtValue.text = context.getString(
                                    keyless.data.utils.android.R.string.text_average
                                )
                            }
                            2 -> {
                                binding.txtValue.text = context.getString(keyless.data.utils.android.R.string.text_good)
                            }
                            3 -> {
                                binding.txtValue.text = context.getString(keyless.data.utils.android.R.string.text_full)
                            }
                            else -> {
                                binding.txtValue.text = context.getString(keyless.data.utils.android.R.string.text_full)
                            }
                        }
                    }
                    2 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.building_name_dot
                        )
                        binding.txtValue.text = lockDetails!!.lock.property_details.name
                    }
                    3 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.floor_number_dot
                        )
                        binding.txtValue.text = lockDetails!!.lock.property_details.floor
                    }
                    4 -> {
                        binding.txtHeading.text = context.getString(
                            keyless.data.utils.android.R.string.apartment_room_number_dot
                        )
                        binding.txtValue.text = lockDetails!!.lock.property_details.appartment_number
                    }
                }
            }
        }
    }
}