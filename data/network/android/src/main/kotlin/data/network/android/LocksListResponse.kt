package data.network.android

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class LocksListResponse() : Parcelable {

    var locks: List<LocksModel> = ArrayList()
    var properties: List<PropertiesMainModel> = ArrayList()
    var success: Boolean = false
    var message: String = ""
    var assignment_id: String = ""
    var logout: Boolean = false
    var is_paid: Boolean = false
    var totalUnreadNotification: Int = 0

    constructor(parcel: Parcel) : this() {
        locks = parcel.createTypedArrayList(LocksModel)!!
        properties = parcel.createTypedArrayList(PropertiesMainModel)!!
        success = parcel.readByte() != 0.toByte()
        message = parcel.readString()!!
        assignment_id = parcel.readString()!!
        logout = parcel.readByte() != 0.toByte()
        is_paid = parcel.readByte() != 0.toByte()
        totalUnreadNotification = parcel.readInt()
    }

    @Keep
    class PropertiesMainModel() : Parcelable {
        var _id: String = ""
        var latitude: String = ""
        var longitude: String = ""
        var manager_id: String = ""
        var manager_type: String = ""
        var emirate: String = ""
        var area: String = ""
        var building_name: String = ""
        var total_floors: Long = 0
        var created_at: String = ""
        var icon_id: String = ""
        var support_call_number: String = ""
        var support_whatsapp_number: String = ""
        var icon: List<IconModel> = ArrayList()
        var count = 0

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            latitude = parcel.readString()!!
            longitude = parcel.readString()!!
            manager_id = parcel.readString()!!
            manager_type = parcel.readString()!!
            emirate = parcel.readString()!!
            area = parcel.readString()!!
            building_name = parcel.readString()!!
            total_floors = parcel.readLong()
            created_at = parcel.readString()!!
            icon_id = parcel.readString()!!
            support_call_number = parcel.readString()!!
            support_whatsapp_number = parcel.readString()!!
            icon = parcel.createTypedArrayList(IconModel)!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(latitude)
            parcel.writeString(longitude)
            parcel.writeString(manager_id)
            parcel.writeString(manager_type)
            parcel.writeString(emirate)
            parcel.writeString(area)
            parcel.writeString(building_name)
            parcel.writeLong(total_floors)
            parcel.writeString(created_at)
            parcel.writeString(icon_id)
            parcel.writeString(support_call_number)
            parcel.writeString(support_whatsapp_number)
            parcel.writeTypedList(icon)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<PropertiesMainModel> {
            override fun createFromParcel(parcel: Parcel): PropertiesMainModel {
                return PropertiesMainModel(parcel)
            }

            override fun newArray(size: Int): Array<PropertiesMainModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class LocksModel() : Parcelable {
        var assignment: AssignmentModel? = AssignmentModel()
        var lock: LockModel = LockModel()
        var owner_id: String = ""
        var unit_id: String = ""
        var booking_number: String = ""
        var privacy: Boolean = false
        var privacy_changed: Boolean = false
        var companyCheckin: Boolean = false
        var checkin: Boolean = false
        var totalCheckins: Int = 0
        var property_details: PropertyDetailsModel = PropertyDetailsModel()
        var passcodeId: String = ""
        var passcode: String = ""

        constructor(parcel: Parcel) : this() {
            assignment = parcel.readParcelable(AssignmentModel::class.java.classLoader)
            lock = parcel.readParcelable(LockModel::class.java.classLoader)!!
            owner_id = parcel.readString()!!
            unit_id = parcel.readString()!!
            booking_number = parcel.readString()!!
            privacy = parcel.readByte() != 0.toByte()
            privacy_changed = parcel.readByte() != 0.toByte()
            companyCheckin = parcel.readByte() != 0.toByte()
            checkin = parcel.readByte() != 0.toByte()
            totalCheckins = parcel.readInt()
            property_details = parcel.readParcelable(PropertyDetailsModel::class.java.classLoader)!!
            passcodeId = parcel.readString()!!
            passcode = parcel.readString()!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeParcelable(assignment, flags)
            parcel.writeParcelable(lock, flags)
            parcel.writeString(owner_id)
            parcel.writeString(unit_id)
            parcel.writeString(booking_number)
            parcel.writeByte(if (privacy) 1 else 0)
            parcel.writeByte(if (privacy_changed) 1 else 0)
            parcel.writeByte(if (companyCheckin) 1 else 0)
            parcel.writeByte(if (checkin) 1 else 0)
            parcel.writeInt(totalCheckins)
            parcel.writeParcelable(property_details, flags)
            parcel.writeString(passcodeId)
            parcel.writeString(passcode)
        }

        fun ttlockOfflinePasscode(): Boolean {
            return lock.provider == "Oji" || lock.provider == "Linko"
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<LocksModel> {
            override fun createFromParcel(parcel: Parcel): LocksModel {
                return LocksModel(parcel)
            }

            override fun newArray(size: Int): Array<LocksModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class PropertyDetailsModel() : Parcelable {
        var id: String = ""
        var floor: String = ""
        var latitude: String = ""
        var longitude: String = ""
        var name: String = ""
        var room_number: String = ""
        var laundary_number: String = ""
        var grocery_number: String = ""
        var maintainance_number: String = ""
        var appartment_number: String = ""
        var support_call_number: String = ""
        var support_whatsapp_number: String = ""
        var map_id: String = ""
        var area: String = ""
        var building_name: String = ""
        var icon: List<IconModel> = ArrayList()

        constructor(parcel: Parcel) : this() {
            id = parcel.readString()!!
            floor = parcel.readString()!!
            latitude = parcel.readString()!!
            longitude = parcel.readString()!!
            name = parcel.readString()!!
            room_number = parcel.readString()!!
            laundary_number = parcel.readString()!!
            grocery_number = parcel.readString()!!
            maintainance_number = parcel.readString()!!
            appartment_number = parcel.readString()!!
            support_call_number = parcel.readString()!!
            support_whatsapp_number = parcel.readString()!!
            map_id = parcel.readString()!!
            area = parcel.readString()!!
            building_name = parcel.readString()!!
            icon = parcel.createTypedArrayList(IconModel)!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(id)
            parcel.writeString(floor)
            parcel.writeString(latitude)
            parcel.writeString(longitude)
            parcel.writeString(name)
            parcel.writeString(room_number)
            parcel.writeString(laundary_number)
            parcel.writeString(grocery_number)
            parcel.writeString(maintainance_number)
            parcel.writeString(appartment_number)
            parcel.writeString(support_call_number)
            parcel.writeString(support_whatsapp_number)
            parcel.writeString(map_id)
            parcel.writeString(area)
            parcel.writeString(building_name)
            parcel.writeTypedList(icon)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<PropertyDetailsModel> {
            override fun createFromParcel(parcel: Parcel): PropertyDetailsModel {
                return PropertyDetailsModel(parcel)
            }

            override fun newArray(size: Int): Array<PropertyDetailsModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class LockModel() : Parcelable {
        var _id: String = ""
        var access_key: String = ""
        var battery_level: Int = -1
        var createdAt: String = ""
        var image: String = ""
        var lock_uid: String = ""
        var name: String = ""
        var desc: String = ""
        var provider: String = ""
        var status: Int = -1
        var unique_key: String = ""
        var unit_id: String = ""
        var time_zone: String = ""
        var internal_id: String = ""
        var encrypted_key: String = ""
        var privacy_mode: Boolean = false
        var primary: Boolean = false
        var privacy_permission: Boolean = false
        var privacy_owner: Boolean = false
        var icon: List<IconModel> = ArrayList()
        var property_details: PropertyDetailsModel = PropertyDetailsModel()
        var firmwareUpdated: Boolean = false
        var firmwareVersion: String = ""
        var firmwareAvailableVersion: String = ""
        var tedeeLockId: String = ""

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            access_key = parcel.readString()!!
            battery_level = parcel.readInt()
            createdAt = parcel.readString()!!
            image = parcel.readString()!!
            lock_uid = parcel.readString()!!
            name = parcel.readString()!!
            desc = parcel.readString()!!
            provider = parcel.readString()!!
            status = parcel.readInt()
            unique_key = parcel.readString()!!
            unit_id = parcel.readString()!!
            time_zone = parcel.readString()!!
            internal_id = parcel.readString()!!
            encrypted_key = parcel.readString()!!
            privacy_mode = parcel.readByte() != 0.toByte()
            primary = parcel.readByte() != 0.toByte()
            privacy_permission = parcel.readByte() != 0.toByte()
            privacy_owner = parcel.readByte() != 0.toByte()
            icon = parcel.createTypedArrayList(IconModel)!!
            property_details = parcel.readParcelable(PropertyDetailsModel::class.java.classLoader)!!
            firmwareUpdated = parcel.readByte() != 0.toByte()
            firmwareVersion = parcel.readString() ?: ""
            firmwareAvailableVersion = parcel.readString() ?: ""
            tedeeLockId = parcel.readString() ?: ""
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(access_key)
            parcel.writeInt(battery_level)
            parcel.writeString(createdAt)
            parcel.writeString(image)
            parcel.writeString(lock_uid)
            parcel.writeString(name)
            parcel.writeString(desc)
            parcel.writeString(provider)
            parcel.writeInt(status)
            parcel.writeString(unique_key)
            parcel.writeString(unit_id)
            parcel.writeString(time_zone)
            parcel.writeString(internal_id)
            parcel.writeString(encrypted_key)
            parcel.writeByte(if (privacy_mode) 1 else 0)
            parcel.writeByte(if (primary) 1 else 0)
            parcel.writeByte(if (privacy_permission) 1 else 0)
            parcel.writeByte(if (privacy_owner) 1 else 0)
            parcel.writeTypedList(icon)
            parcel.writeParcelable(property_details, flags)
            parcel.writeByte(if (firmwareUpdated) 1 else 0)
            parcel.writeString(firmwareVersion)
            parcel.writeString(firmwareAvailableVersion)
            parcel.writeString(tedeeLockId)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<LockModel> {
            override fun createFromParcel(parcel: Parcel): LockModel {
                return LockModel(parcel)
            }

            override fun newArray(size: Int): Array<LockModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class IconModel() : Parcelable {
        var _id: String = ""
        var name: String = ""
        var icon: String = ""
        var type: String = ""
        var createdAt: String = ""

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            name = parcel.readString()!!
            icon = parcel.readString()!!
            type = parcel.readString()!!
            createdAt = parcel.readString()!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(name)
            parcel.writeString(icon)
            parcel.writeString(type)
            parcel.writeString(createdAt)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<IconModel> {
            override fun createFromParcel(parcel: Parcel): IconModel {
                return IconModel(parcel)
            }

            override fun newArray(size: Int): Array<IconModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class AssignmentModel() : Parcelable {
        var assignment_data: AssignmentDataModel = AssignmentDataModel()
        var time_ranges: List<TimeRangeModel> = ArrayList()

        constructor(parcel: Parcel) : this() {
            assignment_data = parcel.readParcelable(AssignmentDataModel::class.java.classLoader)!!
            time_ranges = parcel.createTypedArrayList(TimeRangeModel)!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeParcelable(assignment_data, flags)
            parcel.writeTypedList(time_ranges)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<AssignmentModel> {
            override fun createFromParcel(parcel: Parcel): AssignmentModel {
                return AssignmentModel(parcel)
            }

            override fun newArray(size: Int): Array<AssignmentModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class TimeRangeModel() : Parcelable {
        var _id: String = ""
        var allowed_days: List<Int> = ArrayList()
        var always_open: Boolean = false
        var created_at: String = ""
        var holidays: Boolean = false
        var name: String = ""
        var routine_id: String = ""
        var status: Boolean = false
        var time_slot: TimeSlotModel = TimeSlotModel()

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            allowed_days = parcel.readSerializable() as ArrayList<Int>
            always_open = parcel.readByte() != 0.toByte()
            created_at = parcel.readString()!!
            holidays = parcel.readByte() != 0.toByte()
            name = parcel.readString()!!
            routine_id = parcel.readString()!!
            status = parcel.readByte() != 0.toByte()
            time_slot = parcel.readParcelable(TimeSlotModel::class.java.classLoader)!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeSerializable(allowed_days as ArrayList<Int>)
            parcel.writeByte(if (always_open) 1 else 0)
            parcel.writeString(created_at)
            parcel.writeByte(if (holidays) 1 else 0)
            parcel.writeString(name)
            parcel.writeString(routine_id)
            parcel.writeByte(if (status) 1 else 0)
            parcel.writeParcelable(time_slot, flags)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<TimeRangeModel> {
            override fun createFromParcel(parcel: Parcel): TimeRangeModel {
                return TimeRangeModel(parcel)
            }

            override fun newArray(size: Int): Array<TimeRangeModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class TimeSlotModel() : Parcelable {
        var start_hour: Int = 0
        var start_min: Int = 0
        var end_hour: Int = 0
        var end_min: Int = 0

        constructor(parcel: Parcel) : this() {
            start_hour = parcel.readInt()
            start_min = parcel.readInt()
            end_hour = parcel.readInt()
            end_min = parcel.readInt()
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeInt(start_hour)
            parcel.writeInt(start_min)
            parcel.writeInt(end_hour)
            parcel.writeInt(end_min)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<TimeSlotModel> {
            override fun createFromParcel(parcel: Parcel): TimeSlotModel {
                return TimeSlotModel(parcel)
            }

            override fun newArray(size: Int): Array<TimeSlotModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class AssignmentDataModel() : Parcelable {
        var _id: String = ""
        var assigned_at: String = ""
        var assigned_by: String = ""
        var assigned_to: String = ""
        var lock_id: String = ""
        var time_profile_id: TimeProfileIdModel = TimeProfileIdModel()
        var valid_from: String = ""
        var valid_to: String = ""
        var status: Int = 0

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            assigned_at = parcel.readString()!!
            assigned_by = parcel.readString()!!
            assigned_to = parcel.readString()!!
            lock_id = parcel.readString()!!
            time_profile_id = parcel.readParcelable(TimeProfileIdModel::class.java.classLoader)!!
            valid_from = parcel.readString()!!
            valid_to = parcel.readString()!!
            status = parcel.readInt()
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(assigned_at)
            parcel.writeString(assigned_by)
            parcel.writeString(assigned_to)
            parcel.writeString(lock_id)
            parcel.writeParcelable(time_profile_id, flags)
            parcel.writeString(valid_from)
            parcel.writeString(valid_to)
            parcel.writeInt(status)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<AssignmentDataModel> {
            override fun createFromParcel(parcel: Parcel): AssignmentDataModel {
                return AssignmentDataModel(parcel)
            }

            override fun newArray(size: Int): Array<AssignmentDataModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    class TimeProfileIdModel() : Parcelable {
        var _id: String = ""
        var created_at: String = ""
        var created_by: String = ""
        var iseo_id: Int = 0
        var name: String = ""
        var status: Boolean = false

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            created_at = parcel.readString()!!
            created_by = parcel.readString()!!
            iseo_id = parcel.readInt()
            name = parcel.readString()!!
            status = parcel.readByte() != 0.toByte()
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(created_at)
            parcel.writeString(created_by)
            parcel.writeInt(iseo_id)
            parcel.writeString(name)
            parcel.writeByte(if (status) 1 else 0)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<TimeProfileIdModel> {
            override fun createFromParcel(parcel: Parcel): TimeProfileIdModel {
                return TimeProfileIdModel(parcel)
            }

            override fun newArray(size: Int): Array<TimeProfileIdModel?> {
                return arrayOfNulls(size)
            }
        }
    }

    @Keep
    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedList(locks)
        parcel.writeTypedList(properties)
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeString(message)
        parcel.writeString(assignment_id)
        parcel.writeByte(if (logout) 1 else 0)
        parcel.writeByte(if (is_paid) 1 else 0)
        parcel.writeInt(totalUnreadNotification)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<LocksListResponse> {
        override fun createFromParcel(parcel: Parcel): LocksListResponse {
            return LocksListResponse(parcel)
        }

        override fun newArray(size: Int): Array<LocksListResponse?> {
            return arrayOfNulls(size)
        }
    }
}