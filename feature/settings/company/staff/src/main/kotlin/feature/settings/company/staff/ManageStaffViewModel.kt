package feature.settings.company.staff

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import domain.common.ErrorHandler
import domain.settings.company.staff.manage.models.Event
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class ManageStaffViewModel(
    private val viewModel: domain.settings.company.staff.manage.ViewModel,
    private val onClear: () -> Unit,
    private val handler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    coroutinesContext: CoroutineContext? = null
) : ViewModel() {

    private val scope = if (coroutinesContext != null) viewModelScope + coroutinesContext else viewModelScope

    val screenState = viewModel.screenState.stream 
    
    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}