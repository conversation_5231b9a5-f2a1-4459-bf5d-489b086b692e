package feature.settings.company

import data.settings.company.injection.CompanyProfileDomainScope
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module

val companyProfileFeatureInjection = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<CompanyProfileDomainScope>(CompanyProfileDomainScope.getScopeId())

        CompanyProfileViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = { scope.close() }
        )
    }
}