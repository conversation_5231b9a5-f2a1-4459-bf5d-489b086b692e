package feature.regula.model

import android.graphics.Bitmap
import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
data class DocIntentModel(
    var name: String? = null,
    var identity1: String? = null,
    var identity2: String? = null,
    var documentImageApi: String? = null,
    var myImageApi: String? = null,
    var similarity: String? = null,
    var documentNumber: String? = null,
    var documentType: String? = null,
    var docType: String? = null,
    var docExpiry: String? = null,
    var docStatus: String? = null,
    var expiryDate: String? = null,
    var docSelfie: Bitmap? = null,
    var mySelfie: Bitmap? = null
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(Bitmap::class.java.classLoader),
        parcel.readParcelable(Bitmap::class.java.classLoader)
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(name)
        parcel.writeString(identity1)
        parcel.writeString(identity2)
        parcel.writeString(documentImageApi)
        parcel.writeString(myImageApi)
        parcel.writeString(similarity)
        parcel.writeString(documentNumber)
        parcel.writeString(documentType)
        parcel.writeString(docType)
        parcel.writeString(docExpiry)
        parcel.writeString(docStatus)
        parcel.writeString(expiryDate)
        parcel.writeParcelable(docSelfie, flags)
        parcel.writeParcelable(mySelfie, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DocIntentModel> {
        override fun createFromParcel(parcel: Parcel): DocIntentModel {
            return DocIntentModel(parcel)
        }

        override fun newArray(size: Int): Array<DocIntentModel?> {
            return arrayOfNulls(size)
        }
    }
}