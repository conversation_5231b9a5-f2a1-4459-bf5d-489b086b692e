package data.network.android

import androidx.annotation.Keep

@Keep
class ModelMangeCards {

    var success: Boolean = false
    var total_count: Int = 0
    var data: ArrayList<DataModelCardManage> = ArrayList()
}

@Keep
class ModelCompanyAssign {

    var _id: String = ""
    var card_id: String = ""
    var company_id: String = ""
    var admin_id: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var status: Int = 0
    var __v: Int = 0
}

@Keep
class DataModelCardManage {

    var _id: String = ""
    var card_uid: String = ""
    var admin_id: String = ""
    var internal_id: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var appartment_number: String = ""
    var floor_number: String = ""
    var property_name: String = ""
    var __v: String = ""
    var type: Int = 0
    var status: Int = 0
    var written_on: Int = 0
    var company_assign: ModelCompanyAssign = ModelCompanyAssign()
    var lock_assign: LockAssignModelMange = LockAssignModelMange()
}

@Keep
class UserModel {
    var _id: String = ""
    var first_name: String = ""
    var last_name: String = ""
}

@Keep
class LockAssignModelMange {
    var _id: String = ""
    var card_id: String = ""
    var lock_id: String = ""
    var user_id: String = ""
    var company_id: String = ""
    var admin_id: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var start_time: String = ""
    var end_time: String = ""
    var status: Int = 0
    var __v: Int = 0
    var user: UserModel = UserModel()
    var lock: LockModelManage = LockModelManage()
}

@Keep
class LockModelManage {

    var _id: String = ""
    var name: String = ""
    var unique_key: String = ""
    var access_key: String = ""
    var encrypted_key: String = ""
    var provider: String = ""
}