package core.lock.iseo.models

import com.iseo.v364sdk.services.mobilecredentialservice.model.hermes.IDeviceInfo

fun IDeviceInfo.string(): String {
    return "Level: $level\n" +
        "Time Serial: $timeSerial\n" +
        "UpdateIdx: $upateIdx\n" +
        "BatteryLevel: $batteryLevel\n" +
        "MacAddress: $macAddress\n" +
        "FirmwarePrefix: $firmwarePrefix\n" +
        "VersionAsString: $versionAsString\n" +
        "BoardName: $boardName\n" +
        "LoaderName: $loaderName\n" +
        "BatteryType: $batteryType\n" +
        "DeviceType: $deviceType\n" +
        "DeviceVersion: $deviceVersion"
}