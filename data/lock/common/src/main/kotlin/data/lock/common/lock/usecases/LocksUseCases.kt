package data.lock.common.lock.usecases

import core.monitoring.common.repository.Logger
import data.lock.common.lock.repositories.LocksRemote
import data.lock.common.lock.usecases.lock.LockUseCases
import data.lock.common.lock.usecases.maintenance.MaintenanceUseCases

internal class LocksUseCases(
    remote: LocksRemote,
    logger: Logger
) {

    val maintenance = MaintenanceUseCases(remote = remote, logger = logger)

    val lock = LockUseCases(remote = remote, logger = logger)
}