package feature.pm.addlock

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import data.network.android.models.ModelAdminInstaller
import data.utils.android.CommonValues
import keyless.feature.pm.addlock.R

class AddLockMainActivity : AppCompatActivity() {

    private var list: ModelAdminInstaller.DataModelInstaller = ModelAdminInstaller.DataModelInstaller()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_add_lock_main)
        if (intent.hasExtra("finishActivity")) {
            finish()
        } else if (intent.hasExtra("installer")) {
            list = intent.getParcelableExtra("list")!!
            callFragment("installer")
        } else {
            callFragment("")
        }
    }

    private fun callFragment(whichPlace: String) {
        val bundle = Bundle()
        bundle.putString("whichPlace", whichPlace)
        bundle.putParcelable("list", list)

        if (CommonValues.isBluetoothEnabled()) {
            CommonValues.loadFragment(
                ScanQrFragment(),
                bundle,
                supportFragmentManager,
                R.id.frameContainerAddLock
            )
        } else {
            CommonValues.loadFragment(
                EnableBluetoothFragment(),
                bundle,
                supportFragmentManager,
                R.id.frameContainerAddLock
            )
        }
    }
}