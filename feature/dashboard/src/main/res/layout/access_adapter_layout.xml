<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="20dp">


    <ImageView
        android:id="@+id/ivStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_tick"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/txtPersonsName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="16dp"
        style="@style/mirrorText"
        android:fontFamily="@font/poppins_medium_500"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/txtDate"
        app:layout_constraintStart_toEndOf="@+id/ivStatus"
        app:layout_constraintTop_toTopOf="@+id/ivStatus" />

    <TextView
        android:id="@+id/txtDeviceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        style="@style/mirrorText"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/txtPersonsName"
        app:layout_constraintStart_toStartOf="@+id/txtPersonsName"
        app:layout_constraintTop_toBottomOf="@+id/txtPersonsName" />

    <TextView
        android:id="@+id/txtDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        style="@style/mirrorText"
        android:includeFontPadding="false"
        android:textColor="@color/color_grey"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivStatus"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivStatus" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:background="#EBEBEB"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtDeviceName" />


</androidx.constraintlayout.widget.ConstraintLayout>