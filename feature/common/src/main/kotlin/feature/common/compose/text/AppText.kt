package feature.common.compose.text

import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import feature.common.compose.theme.mainFontFamily

@Composable
fun AppPageTitleText(
    modifier: Modifier = Modifier,
    text: String,
    alignment: TextAlign = TextAlign.Start
) {
    Text(
        modifier = modifier,
        text = text,
        color = MaterialTheme.colorScheme.onPrimary,
        fontWeight = FontWeight.Medium,
        fontSize = 18.sp,
        fontFamily = mainFontFamily,
        textAlign = alignment
    )
}

@Composable
fun AppLabelText(
    modifier: Modifier = Modifier,
    text: String,
    alignment: TextAlign = TextAlign.Start,
    color: Color = MaterialTheme.colorScheme.onBackground
) {
    Text(
        modifier = modifier,
        text = text,
        color = color,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        fontFamily = mainFontFamily,
        textAlign = alignment
    )
}

@Composable
fun AppBodyText(
    modifier: Modifier = Modifier,
    text: String,
    alignment: TextAlign = TextAlign.Start,
    weight: FontWeight = FontWeight.Normal
) {
    Text(
        modifier = modifier,
        text = text,
        color = MaterialTheme.colorScheme.onBackground,
        fontWeight = weight,
        fontSize = 16.sp,
        fontFamily = mainFontFamily,
        textAlign = alignment
    )
}