package feature.dfu.repo

import android.content.Context
import android.net.Uri
import android.util.Log
import feature.dfu.data.DeviceFirmUpdateManager
import feature.dfu.data.DeviceFirmUpdateSettings
import feature.dfu.data.DeviceTarget
import feature.dfu.data.DfuFileManager
import feature.dfu.data.DfuProgressManager
import feature.dfu.data.DfuState
import feature.dfu.data.FirmwareZipFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import no.nordicsemi.android.dfu.DfuServiceController
import javax.inject.Inject

class DfuRepo @Inject constructor(
    private val runningObserver: DFUServiceRunningObserver,
    private val fileManger: DfuFileManager,
    private val dfuManager: DeviceFirmUpdateManager,
    private val progressManager: DfuProgressManager
) {
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    private val _data = MutableStateFlow<DfuState>(DfuState.Idle)
    val data: StateFlow<DfuState> = _data.asStateFlow()

    var deviceTarget: DeviceTarget? = null

    var target: DeviceTarget? = null

    private var zipFile: FirmwareZipFile? = null
    private var dfuServiceController: DfuServiceController? = null

    init {
        progressManager.registerListener()
        progressManager.status
            .onEach { _data.value = it }
            .launchIn(scope)
    }

    fun setZipFile(file: Uri) = fileManger.createFile(file)?.also {
        zipFile = it
    }

    fun setZipFile(file: Uri, context: Context) = fileManger.createFile(file, context)?.also {
        zipFile = it
    }

    fun launch(settings: DeviceFirmUpdateSettings) {
        progressManager.start()

//        dfuServiceController = dfuManager.install(zipFile!!, target!!, settings)
        Log.e("TAG", "launch: $zipFile   $target")
        dfuServiceController = dfuManager.install(zipFile!!, target!!)
    }
//    fun launch(settings: DeviceFirmUpdateSettings , file: File) {
//        progressManager.start()
//
//        val zFile = FirmwareZipFile(file.toUri() , file.name , file.path , file.length())
//
//        dfuServiceController = dfuManager.install(zipFile!!, target!!, settings)
//    }

    fun abort() {
        dfuServiceController?.abort()
    }

    fun isRunning(): Boolean {
        return runningObserver.isRunning
    }

    fun openLogger() {
        dfuManager.openLogger()
    }

    fun release() {
        target = null
        zipFile = null
        dfuServiceController = null
        progressManager.release()
        progressManager.unregisterListener()
    }
}