package feature.masterkey

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.blekey.sdk.bean.KeyInfoBean
import com.blekey.sdk.bean.KeyReportInfoBean
import com.blekey.sdk.bean.ResultBean
import com.blekey.sdk.ble.BleKeySdk
import com.blekey.sdk.ble.BleKeySdkCallback
import com.blekey.sdk.data.KeyBasicInfo
import com.blekey.sdk.data.KeyEventInfo
import com.google.gson.JsonObject
import com.wdullaer.materialdatetimepicker.time.TimePickerDialog
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import data.utils.android.CommonValues.Companion.ADD_ADAPTER
import data.utils.android.common.BleLockScanData
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.master.key.databinding.FragmentConfigureMasterKeyBinding
import java.text.DateFormat
import java.text.DateFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class ConfigureMasterKeyFragment : Fragment() {

    private lateinit var validDateApi: Date
    private var sysCodeForConnect: String = ""
    private val lockListName: ArrayList<String> = ArrayList()
    private var accessKey: String = ""
    private val lockList: ArrayList<LocksListResponse.LockModel> = ArrayList()
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    private var mBleKeySdk: BleKeySdk? = null
    private var mKeyBasicInfo: KeyInfoBean? = null
    private var mMac: String? = null
    private lateinit var mBleHandler: BleHandler
    lateinit var mViewModel: MasterKeyViewModel
    private lateinit var binding: FragmentConfigureMasterKeyBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentConfigureMasterKeyBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        getData()
        clickListeners()
    }

    private fun initz() {
        mViewModel = ViewModelProvider(requireActivity())[MasterKeyViewModel::class.java]
    }

    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                ADD_ADAPTER -> addAdapterItem(msg.obj as String)
                else -> {}
            }
        }
    }

    private fun addAdapterItem(data: String) {
    }

    private fun getData() {
        val arguments = arguments?.getParcelable<BleLockScanData>("keyName")
        binding.etSelectedKey.setText(arguments?.bleName)
        val getLockData = sharePrefs.getLockData()

        val requestBody = JsonObject()
        requestBody.addProperty("key_value", arguments?.bleName.toString())
        mViewModel.getSysCodeHistory(requestBody, sharePrefs.token).observe(requireActivity()) {
            sysCodeForConnect = it.data.sys_code
        }

        lockList.add(0, LocksListResponse.LockModel())
        lockListName.add(0,getString(keyless.data.utils.android.R.string.select_lock))

        for (i in getLockData) {
            if (i.lock.provider == "Keyless") {
                lockList.add(i.lock)
                lockListName.add(i.lock.name)
            }
        }

        mBleHandler = BleHandler()
        mMac = arguments?.bleMac
        mBleKeySdk = BleKeySdk()
        mKeyBasicInfo = KeyInfoBean()
        mBleKeySdk!!.init(requireActivity(), mMac, "", sBleKeySdkCallback)
    }

    private fun clickListeners() {
        val adapter = ArrayAdapter(
            requireActivity(),
            android.R.layout.simple_spinner_item,
            lockListName
        )
        binding.txtSelectLock.adapter = adapter

        binding.txtSelectLock.onItemSelectedListener = object :
            AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>,
                view: View,
                position: Int,
                id: Long
            ) {
                accessKey = lockList[position].access_key
                Log.d("//////" + accessKey.toByteArray(), "")
            }

            override fun onNothingSelected(parent: AdapterView<*>) {
                // write code to perform some action
            }
        }

        binding.viewBack.setOnClickListener {
            mBleKeySdk?.disconnect()
            requireFragmentManager().popBackStack()
        }

        binding.btnConfigure.setOnClickListener {
            if (accessKey.isEmpty()) {
                requireActivity().toast(
                    getString(keyless.data.utils.android.R.string.please_select_lock_to_configure_key)
                )
            } else if (binding.selectTimeZone.text.toString().isEmpty()) {
                requireActivity().toast(getString(keyless.data.utils.android.R.string.please_enter_valid_to_date))
            } else {
                binding.progressLay.isVisible = true
                mBleKeySdk!!.connect(
                    "8qli5ljbkBJSBip8".toByteArray(),
                    accessKey.toByteArray(),
                    sysCodeForConnect.toByteArray(),
                    Date().time,
                    KeyBasicInfo.EN,
                    true,
                    false
                )
            }
        }

        binding.selectTimeZone.setOnClickListener {
            binding.selectTimeZone.isEnabled = false
            val currentDateTime = Calendar.getInstance()
            currentDateTime.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            val startYear = currentDateTime.get(Calendar.YEAR)
            val startMonth = currentDateTime.get(Calendar.MONTH)
            val startDay = currentDateTime.get(Calendar.DAY_OF_MONTH)
            val startHour = currentDateTime.get(Calendar.HOUR_OF_DAY)
            val startMinute = currentDateTime.get(Calendar.MINUTE)
            try {
                var datePickerDialog = DatePickerDialog(
                    requireActivity(),
                    { _, year, month, day ->

                        val tpd: TimePickerDialog = TimePickerDialog.newInstance(
                            { view, hourOfDay, minute, second ->
                                val pickedDateTime = Calendar.getInstance()
                                pickedDateTime.timeZone =
                                    TimeZone.getTimeZone(Preferences.timeZoneName.get())

                                pickedDateTime.set(year, month, day, hourOfDay, minute)
                                var mMonth = (month + 1).toString()
                                var mDay = (day).toString()
                                var mHour = (hourOfDay).toString()
                                var mMinute = (minute).toString()

                                if (mMonth.length == 1) {
                                    mMonth = "0$mMonth"
                                }
                                if (mDay.length == 1) {
                                    mDay = "0$mDay"
                                }
                                if (mHour.length == 1) {
                                    mHour = "0$mHour"
                                }
                                if (mMinute.length == 1) {
                                    mMinute = "0$mMinute"
                                }
                                val month = DateFormatSymbols.getInstance(Locale("en")).shortMonths[mMonth.toInt() - 1]
                                binding.selectTimeZone.setText("$mDay-$month-$year $mHour:$mMinute")
                                var dateForApi = "$year-$mMonth-$mDay $mHour:$mMinute"

                                val formatter: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm",Locale("en"))
                                val date = formatter.parse(dateForApi) as Date

                                println("Today is " + date.time)
                                validDateApi = date
                            },
                            startHour,
                            startMinute,
                            false
                        )

                        var mon = month + 1
                        var m = mon.toString()
                        m = if (month.toString().length == 1) {
                            "0$mon"
                        } else {
                            mon.toString()
                        }
                        var d = ""
                        d = if (day.toString().length == 1) {
                            "0$day"
                        } else {
                            day.toString()
                        }

                        var isDateSame = false

                        var dateStart =
                            startDay.toString() + "/" + (startMonth + 1) + "/" + startYear
                        var dateEnd = "$d/$m/$year"

                        try {
                            val formatter = SimpleDateFormat("dd/MM/yyyy")
                            val str1 = dateStart
                            val date1 = formatter.parse(str1)
                            val str2 = dateEnd
                            val date2 = formatter.parse(str2)
                            isDateSame = date1 == date2
                        } catch (e1: ParseException) {
                            e1.printStackTrace()
                        }

                        if (isDateSame) {
                            tpd.setMinTime(startHour, startMinute, 0)
                        }

                        if (isDateSame) {
                            tpd.setMinTime(startHour, startMinute, 0)
                        }
                        tpd.show(requireActivity().supportFragmentManager, "")
                    },

                    startYear,
                    startMonth,
                    startDay

                )
                datePickerDialog.datePicker.minDate = System.currentTimeMillis() - 1000
                datePickerDialog.show()
                binding.selectTimeZone.isEnabled = true
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private val sBleKeySdkCallback: BleKeySdkCallback = object : BleKeySdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """init sdk  
     ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        @SuppressLint("MissingInflatedId")
        override fun connect(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                connect key  
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
            if (resultBean!!.isRet) {
                activity!!.runOnUiThread {
                    mBleKeySdk?.registerKey(
                        accessKey.trim { it <= ' ' }
                            .toByteArray(),
                        accessKey.trim { it <= ' ' }
                            .toByteArray()
                    )
                }
            } else {
//                progressLay.isVisible = false
                activity!!.runOnUiThread {
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.the_master_key_could_not_be_connected),
                        object : OnActionOK {
                            override fun onClickData() {
                                binding.progressLay.isVisible = false
                                mBleKeySdk?.disconnect()
                            }
                        }
                    )
                }
            }
        }

        override fun rssi(rssi: Int) {
//            mTvRssi.setText(rssi.toString())
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                disconnect key  
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        override fun getKeyInfo(resultBean: ResultBean<KeyInfoBean>) {
            if (null != resultBean && resultBean.isRet && resultBean.obj is KeyInfoBean) {
                mKeyBasicInfo = resultBean.obj as KeyInfoBean
            }
        }

        override fun resetKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """reset key ${JSON.toJSONString(resultBean)}""".trimIndent()
            ).sendToTarget()
        }

        override fun registerKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                register key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()

            if (resultBean!!.isRet) {
                activity!!.runOnUiThread {
                    val requestBody = JsonObject()
                    requestBody.addProperty("key_value", binding.etSelectedKey.text.toString())
                    requestBody.addProperty("sys_code", accessKey)
                    mViewModel.setSysCodeHistorySubmit(requestBody, sharePrefs.token)
                        .observe(requireActivity()) {
                            if (it.success) {
                                Thread.sleep(3000)
                                val begin = Calendar.getInstance()
                                begin.timeZone =
                                    TimeZone.getTimeZone(Preferences.timeZoneName.get())
                                begin.add(Calendar.MINUTE, -1)
                                val date = Date(validDateApi.time)
                                println(date)
                                mBleKeySdk!!.setEmergencyKey(1, begin.time, date, "")
                            }
                        }
                }
            } else {
                binding.progressLay.isVisible = false
            }
        }

        /**
         * //制作设置锁号钥匙
         * @param resultBean`
         */
        override fun setSettingKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set setting lock id key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作修改锁系统码钥匙
         * @param resultBean
         */
        override fun setRegisterKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set register lock key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作工程钥匙
         * @param resultBean
         */
        override fun setConstructionKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set construction key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作紧急钥匙
         * @param resultBean
         */
        override fun setEmergencyKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set emergency key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
            if (resultBean!!.isRet) {
                binding.progressLay.isVisible = false
                activity!!.runOnUiThread {
                    mBleKeySdk!!.getKeyInfo()
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.your_emergency_key_is_set),
                        object : OnActionOK {
                            override fun onClickData() {
                                requireActivity().finish()
                            }
                        }
                    )
                }
            } else {
                binding.progressLay.isVisible = false
            }
        }

        /**
         * //制作读取锁内事件钥匙
         * @param resultBean
         */
        override fun setAuditKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set autid key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作读取锁号钥匙
         * @param resultBean
         */
        override fun setVerifyKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set verify key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作读取锁硬件序列号钥匙
         * @param resultBean
         */
        override fun setTraceKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set trace key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作黑名单钥匙
         * @param resultBean
         */
        override fun setBlackListKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                blacklist key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作用户钥匙
         * @param resultBean
         */
        override fun setUserKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set user key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //制作蓝牙在线开门
         * @param resultBean
         */
        override fun setOnlineOpen(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set user key online open 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        override fun setAuxiliaryKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set auxiliary key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        override fun setAdvancedKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set advanced key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        override fun setElectricityKey(resultBean: ResultBean<*>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                """
                set electricity key 
                ${JSON.toJSONString(resultBean)}
                """.trimIndent()
            ).sendToTarget()
        }

        /**
         * //读取钥匙内事件
         * @param resultBean
         */
        override fun readKeyEvent(resultBean: ResultBean<KeyEventInfo>) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                "read key events  \n" + JSON.toJSONString(
                    resultBean,
                    SerializerFeature.WriteDateUseDateFormat
                )
            ).sendToTarget()
        }

        /**
         * //钥匙与锁交互主动上报事件
         * @param resultBean
         */
        override fun onReport(resultBean: ResultBean<KeyReportInfoBean<*>?>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                "key report  \n" + JSON.toJSONString(
                    resultBean,
                    SerializerFeature.WriteDateUseDateFormat
                )
            ).sendToTarget()
        }

        /**
         * //清楚钥匙内存储的事件
         * @param resultBean
         */
        override fun cleanKeyEvent(resultBean: ResultBean<KeyReportInfoBean<*>?>?) {
            mBleHandler.obtainMessage(
                ADD_ADAPTER,
                "clean key event  \n" + JSON.toJSONString(
                    resultBean,
                    SerializerFeature.WriteDateUseDateFormat
                )
            ).sendToTarget()
        }
    }
}