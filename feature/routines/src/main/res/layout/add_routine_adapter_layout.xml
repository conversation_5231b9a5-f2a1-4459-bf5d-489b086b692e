<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:theme="@style/Theme.MaterialComponents.Light"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:strokeColor="@color/card_stroke_access"
    app:strokeWidth="1dp"
    app:cardCornerRadius="18dp"
    card_view:cardElevation="0dp"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">


        <ImageView
            android:id="@+id/deleteItem"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/iv_delete_routine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView29"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/start_time"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/textView30"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/end_time"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="@+id/guideline4"
            app:layout_constraintTop_toTopOf="@+id/textView29" />


        <EditText
            android:id="@+id/etStartTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/enter_start_time"
            android:minHeight="48dp"
            android:padding="10dp"
            android:singleLine="true"
            android:focusable="false"
            android:ellipsize="end"
            android:clickable="true"
            android:editable="true"
            style="@style/mirrorText"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:visibility="visible"
            app:layout_constraintEnd_toStartOf="@+id/guideline4"
            app:layout_constraintStart_toStartOf="@+id/textView29"
            app:layout_constraintTop_toBottomOf="@+id/textView29" />


        <EditText
            android:id="@+id/etEndTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/enter_end_time"
            android:focusable="false"
            android:clickable="true"
            style="@style/mirrorText"
            android:editable="true"
            android:minHeight="48dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:padding="10dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/textView30"
            app:layout_constraintTop_toBottomOf="@+id/textView29" />


        <TextView
            android:id="@+id/textView31"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/select_day"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etStartTime" />


        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:scrollbars="none"
            app:layout_constraintStart_toStartOf="@+id/textView31"
            app:layout_constraintTop_toBottomOf="@+id/textView31">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/txtMon"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/mon"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txtTues"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/tue"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txtWed"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/wed"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txtThurs"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/thu"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txtFri"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/fri"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txtSat"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/sat"
                    android:textColor="@color/black"
                    android:textSize="14dp" />


                <TextView
                    android:id="@+id/txtSun"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/iv_grey_rounded"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/sun"
                    android:textColor="@color/black"
                    android:textSize="14dp" />


            </LinearLayout>

        </HorizontalScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>