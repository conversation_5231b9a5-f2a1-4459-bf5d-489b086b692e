package data.common

import data.common.preferences.Preferences
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

fun Instant.device() = toLocalDateTime(TimeZone.currentSystemDefault())
fun Instant.isAfter(other: Instant) = this > other
fun Instant.isBefore(other: Instant) = this < other

val Instant.localDate get() = toLocalDateTime(TimeZone.currentSystemDefault()).date
fun LocalDate.isAfter(other: LocalDate) = this > other
fun LocalDate.isBefore(other: LocalDate) = this < other

fun now(): Instant = Clock.System.now()
fun lastServerTime() = runCatching { Instant.parse(Preferences.lastOnlineLocksFetchDateTime.get()) }.getOrNull()