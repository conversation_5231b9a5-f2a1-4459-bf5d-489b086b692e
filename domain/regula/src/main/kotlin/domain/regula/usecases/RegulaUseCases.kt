package domain.regula.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.regula.documents.RegulaDocuments
import core.regula.documents.RegulaFace
import data.user.account.repositories.UserAccountRepository
import domain.regula.repositories.ScreenStateRepository
import domain.regula.repositories.SideEffectsRepository

internal class RegulaUseCases(
    screen: ScreenStateRepository,
    sideEffects: SideEffectsRepository,
    documents: RegulaDocuments,
    face: RegulaFace,
    user: UserAccountRepository,
    status: StatusRepository,
    logger: Logger
) {
    val checkIn = CheckInUseCase(screen, sideEffects, user, logger, status)
    val face = FaceUseCase(screen, sideEffects, logger, face, status)
    val screen = ScreenUseCase(documents, face, screen, sideEffects, logger, status)
    val scan = ScanUseCase(screen, logger, documents, status)
}