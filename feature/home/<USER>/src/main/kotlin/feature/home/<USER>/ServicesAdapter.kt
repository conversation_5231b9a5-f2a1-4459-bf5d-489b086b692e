package feature.home.locks

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import keyless.feature.home.locks.databinding.ServicesAdapterLayoutBinding

class ServicesAdapter(
    private val context: Context,
    val dataList: ArrayList<ServicesModel>,
    lockHomeFragment: Fragment
) :
    RecyclerView.Adapter<ServicesAdapter.ViewHolder>() {

    var listener = lockHomeFragment as ClickForApi

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ServicesAdapterLayoutBinding.inflate(LayoutInflater.from(context), viewGroup, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind()
    }

    private fun launchLink(context: Context, link: String) {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(link))
        context.startActivity(browserIntent)
    }

    override fun getItemCount() = dataList.size

    inner class ViewHolder(val binding: ServicesAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.imageView.setImageResource(dataList[position].image)
            binding.tvServiceName.text = dataList[position].text
            itemView.setOnClickListener {
                listener.clicking(dataList, position)
            }
        }
    }

    interface ClickForApi {
        fun clicking(model: ArrayList<ServicesModel>, position: Int)
    }
}