package feature.settings.support.diagnostics

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import data.common.preferences.Preferences
import domain.common.ErrorHandler
import domain.settings.support.models.DiagnosticEvent
import domain.settings.support.models.SupportDomainScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.getScopeId

class DiagnosticsViewModel : ViewModel(), KoinComponent {

    private val viewModel: domain.settings.support.ViewModel by getKoin()
        .getOrCreateScope<SupportDomainScope>(SupportDomainScope.getScopeId())
        .inject()
    private val handler: <PERSON>rror<PERSON><PERSON><PERSON> by getKoin().inject()
    internal val sideEffects = viewModel.sideEffectsStream

    fun sendErrorLogs(internalId: String) {
        viewModelScope.launch {
            handler.async {
                viewModel.onEvent(
                    DiagnosticEvent.SendLogs(
                        internalId = internalId,
                        authentication = Preferences.authenticationToken.get()
                    )
                )
            }
        }
    }

    override fun onCleared() {
        getKoin()
            .getOrCreateScope<SupportDomainScope>(SupportDomainScope.getScopeId())
            .close()
        super.onCleared()
    }
}